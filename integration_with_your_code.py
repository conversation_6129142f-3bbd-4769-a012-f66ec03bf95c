#!/usr/bin/env python3
"""
Пример интеграции решателя reCAPTCHA с вашим существующим кодом
"""

from linken_sphere_recaptcha_solver import LinkenSphereRecaptchaSolver
from recaptcha_config import *
import logging

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOGGING_CONFIG["log_file"], encoding=LOGGING_CONFIG["encoding"])
    ]
)
logger = logging.getLogger(__name__)

class YourExistingBrowserManager:
    """
    ЗАМЕНИТЕ ЭТОТ КЛАСС НА ВАШ РЕАЛЬНЫЙ КОД
    для создания браузера с прокси через Linken Sphere
    """
    
    def __init__(self, profile_id, proxy_config=None):
        self.profile_id = profile_id
        self.proxy_config = proxy_config
        self.browser_port = None
        
    def create_browser_with_proxy(self):
        """
        ВАШ КОД для создания браузера с прокси
        Должен возвращать порт браузера или None при ошибке
        """
        # Пример вашего кода:
        # 1. Настройка прокси в профиле
        # 2. Запуск профиля через Linken Sphere API
        # 3. Получение порта браузера
        
        logger.info("🔧 Создание браузера с прокси (ваш код)")
        
        # Здесь должен быть ваш реальный код
        # Например:
        try:
            # Ваш код для настройки прокси и запуска браузера
            # ...
            
            # Возвращаем порт браузера (пример)
            self.browser_port = 9222  # Замените на реальный порт
            logger.info(f"✅ Браузер создан на порту {self.browser_port}")
            return self.browser_port
            
        except Exception as e:
            logger.error(f"❌ Ошибка создания браузера: {e}")
            return None
    
    def navigate_to_site(self, url):
        """ВАШ КОД для перехода на сайт"""
        logger.info(f"🌐 Переход на сайт: {url}")
        # Ваш код навигации
        return True
    
    def close_browser(self):
        """ВАШ КОД для закрытия браузера"""
        logger.info("🔚 Закрытие браузера")
        # Ваш код закрытия


class IntegratedCaptchaSolver:
    """Интегрированный решатель капчи с вашим кодом"""
    
    def __init__(self, profile_id, proxy_config=None):
        self.profile_id = profile_id
        self.proxy_config = proxy_config
        
        # Ваш менеджер браузера
        self.browser_manager = YourExistingBrowserManager(profile_id, proxy_config)
        
        # Решатель капчи
        self.captcha_solver = None
        
        logger.info("🔧 Инициализация интегрированного решателя")
    
    def setup_browser(self):
        """Настройка браузера с использованием вашего кода"""
        try:
            # Используем ваш существующий код для создания браузера
            browser_port = self.browser_manager.create_browser_with_proxy()
            
            if browser_port:
                # Создаем решатель капчи
                self.captcha_solver = LinkenSphereRecaptchaSolver(
                    cap_guru_api_key=CAP_GURU_API_KEY,
                    linken_sphere_api_url=LINKEN_SPHERE_CONFIG["api_url"],
                    profile_id=self.profile_id
                )
                
                # Устанавливаем порт браузера (модифицируем решатель)
                self.captcha_solver.browser_port = browser_port
                
                logger.info("✅ Браузер настроен успешно")
                return True
            else:
                logger.error("❌ Не удалось создать браузер")
                return False
                
        except Exception as e:
            logger.error(f"❌ Ошибка при настройке браузера: {e}")
            return False
    
    def solve_captcha_on_current_page(self):
        """Решение капчи на текущей странице"""
        if not self.captcha_solver:
            logger.error("❌ Решатель капчи не инициализирован")
            return False
        
        try:
            logger.info("🎯 Начало решения капчи на текущей странице")
            
            # Подключаемся к уже запущенному браузеру
            if not self.captcha_solver.connect_to_existing_browser():
                logger.error("❌ Не удалось подключиться к браузеру")
                return False
            
            # Ищем и кликаем чекбокс
            if not self.captcha_solver.find_recaptcha_checkbox():
                logger.error("❌ Не удалось найти чекбокс")
                return False
            
            # Получаем задание
            instruction, image = self.captcha_solver.get_recaptcha_challenge()
            if not instruction or not image:
                logger.error("❌ Не удалось получить задание")
                return False
            
            # Отправляем на решение
            task_id = self.captcha_solver.solve_captcha_with_cap_guru(instruction, image)
            if not task_id:
                logger.error("❌ Не удалось отправить на решение")
                return False
            
            # Получаем координаты
            coordinates = self.captcha_solver.get_captcha_result(task_id)
            if not coordinates:
                logger.error("❌ Не удалось получить координаты")
                return False
            
            # Кликаем по координатам
            if not self.captcha_solver.click_coordinates(coordinates):
                logger.error("❌ Не удалось кликнуть по координатам")
                return False
            
            logger.info("🎉 Капча решена успешно!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка решения капчи: {e}")
            return False
    
    def full_workflow(self, target_url):
        """Полный рабочий процесс: создание браузера + переход + решение капчи"""
        try:
            logger.info("🚀 Запуск полного рабочего процесса")
            
            # Шаг 1: Настройка браузера
            if not self.setup_browser():
                return False
            
            # Шаг 2: Переход на сайт (ваш код)
            if not self.browser_manager.navigate_to_site(target_url):
                logger.error("❌ Не удалось перейти на сайт")
                return False
            
            # Шаг 3: Решение капчи
            if not self.solve_captcha_on_current_page():
                return False
            
            logger.info("🎉 Полный рабочий процесс завершен успешно!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка в рабочем процессе: {e}")
            return False
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Очистка ресурсов"""
        try:
            if self.captcha_solver:
                self.captcha_solver.cleanup()
            
            if self.browser_manager:
                self.browser_manager.close_browser()
                
            logger.info("✅ Ресурсы очищены")
            
        except Exception as e:
            logger.error(f"❌ Ошибка очистки: {e}")


def main():
    """Пример использования интегрированного решателя"""
    
    # Проверяем конфигурацию
    if not print_config_summary():
        print("❌ Исправьте ошибки конфигурации и запустите снова")
        return
    
    # Настройки
    PROFILE_ID = LINKEN_SPHERE_CONFIG["profile_id"]
    TARGET_URL = TEST_URLS["google_demo"]  # Или ваш сайт
    
    # Конфигурация прокси (замените на вашу)
    PROXY_CONFIG = {
        "type": "http",  # http, socks5
        "host": "proxy.example.com",
        "port": 8080,
        "username": "user",
        "password": "pass"
    }
    
    print(f"🎯 Целевой сайт: {TARGET_URL}")
    print(f"👤 Профиль: {PROFILE_ID}")
    
    # Создаем интегрированный решатель
    solver = IntegratedCaptchaSolver(
        profile_id=PROFILE_ID,
        proxy_config=PROXY_CONFIG
    )
    
    try:
        # Запускаем полный рабочий процесс
        success = solver.full_workflow(TARGET_URL)
        
        if success:
            print("\n🎉 КАПЧА РЕШЕНА УСПЕШНО!")
            print("📸 Проверьте скриншоты в папке screenshots/")
        else:
            print("\n❌ НЕ УДАЛОСЬ РЕШИТЬ КАПЧУ")
            print("📋 Проверьте логи для диагностики")
        
        input("\nНажмите Enter для завершения...")
        
    except KeyboardInterrupt:
        print("\n⏹️ Процесс прерван пользователем")
    except Exception as e:
        print(f"\n💥 Критическая ошибка: {e}")
    finally:
        solver.cleanup()


if __name__ == "__main__":
    main()
