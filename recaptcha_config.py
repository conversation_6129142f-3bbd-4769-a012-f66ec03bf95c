#!/usr/bin/env python3
"""
Конфигурация для решателя reCAPTCHA v2 с Linken Sphere и cap.guru
"""

# =============================================================================
# ОСНОВНЫЕ НАСТРОЙКИ
# =============================================================================

# API ключ для сервиса cap.guru
# Получить можно на: https://cap.guru/
CAP_GURU_API_KEY = "47ff86fb043f86d6c563dca6ffbc2328"

# Настройки Linken Sphere API
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:50325",  # URL API Linken Sphere
    "profile_id": "Desktop preset 1",    # ID профиля из Linken Sphere
    "timeout": 30,                       # Таймаут для запросов в секундах
}

# Настройки cap.guru API
CAP_GURU_CONFIG = {
    "base_url": "http://api.cap.guru",
    "timeout": 30,                       # Таймаут для HTTP запросов
    "max_attempts": 30,                  # Максимальное количество попыток получения результата
    "check_interval": 5,                 # Интервал проверки результата в секундах
}

# =============================================================================
# СЕЛЕКТОРЫ ДЛЯ reCAPTCHA ЭЛЕМЕНТОВ
# =============================================================================

RECAPTCHA_SELECTORS = {
    # Основной iframe с reCAPTCHA
    "main_iframe": "iframe[src*='recaptcha']",
    
    # iframe с заданием капчи
    "challenge_iframe": "iframe[src*='bframe']",
    
    # Чекбокс "Я не робот"
    "checkbox_selectors": [
        "#recaptcha-anchor",
        ".recaptcha-checkbox",
        ".recaptcha-checkbox-border",
        "[role='checkbox']"
    ],
    
    # Инструкции к заданию
    "instruction_selectors": [
        ".rc-imageselect-desc-no-canonical",
        ".rc-imageselect-desc",
        ".rc-imageselect-instructions",
        "[class*='instruction']"
    ],
    
    # Изображение капчи
    "image_selectors": [
        ".rc-image-tile-wrapper img",
        ".rc-imageselect-payload img",
        "img[src*='payload']",
        ".rc-imageselect-target img"
    ],
    
    # Область для кликов по изображению
    "image_area_selectors": [
        ".rc-imageselect-payload",
        ".rc-image-tile-wrapper"
    ],
    
    # Кнопка подтверждения
    "verify_button_selectors": [
        "#recaptcha-verify-button",
        ".rc-button-default",
        "[id*='verify']",
        "button[type='submit']"
    ],
    
    # Кнопка обновления
    "reload_button": "#recaptcha-reload-button"
}

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

LOGGING_CONFIG = {
    "level": "INFO",                     # Уровень логирования: DEBUG, INFO, WARNING, ERROR
    "console_output": True,              # Выводить логи в консоль
    "file_output": True,                 # Сохранять логи в файл
    "log_file": "recaptcha_solver.log",  # Имя файла для логов
    "encoding": "utf-8"                  # Кодировка файла логов
}

# =============================================================================
# НАСТРОЙКИ СКРИНШОТОВ
# =============================================================================

SCREENSHOT_CONFIG = {
    "enabled": True,                     # Включить сохранение скриншотов
    "directory": "screenshots",          # Папка для скриншотов
    "format": "png",                     # Формат файлов (png, jpg)
    "quality": 95,                       # Качество для jpg (1-100)
    "prefix": "",                        # Префикс для имен файлов
}

# =============================================================================
# ТАЙМАУТЫ И ОЖИДАНИЯ
# =============================================================================

TIMEOUTS = {
    "page_load": 30,                     # Таймаут загрузки страницы
    "element_wait": 15,                  # Ожидание появления элементов
    "implicit_wait": 10,                 # Неявное ожидание Selenium
    "after_click": 3,                    # Пауза после клика
    "before_challenge": 5,               # Пауза перед получением задания
    "between_clicks": 1,                 # Пауза между кликами по координатам
}

# =============================================================================
# НАСТРОЙКИ БРАУЗЕРА
# =============================================================================

BROWSER_CONFIG = {
    "window_size": "1920,1080",          # Размер окна браузера
    "user_agent": None,                  # Пользовательский User-Agent (None = по умолчанию)
    "disable_images": False,             # Отключить загрузку изображений
    "disable_javascript": False,         # Отключить JavaScript
    "headless": False,                   # Запуск в фоновом режиме
}

# =============================================================================
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# =============================================================================

ADDITIONAL_CONFIG = {
    "debug_mode": False,                 # Режим отладки (больше логов)
    "save_captcha_images": True,         # Сохранять изображения капчи отдельно
    "retry_on_failure": True,            # Повторять попытки при неудаче
    "max_retries": 3,                    # Максимальное количество повторов
    "cleanup_on_exit": True,             # Очищать ресурсы при выходе
}

# =============================================================================
# ТЕСТОВЫЕ URL
# =============================================================================

TEST_URLS = {
    "google_demo": "https://www.google.com/recaptcha/api2/demo",
    "recaptcha_demo": "https://recaptcha-demo.appspot.com/recaptcha-v2-checkbox.php",
    "custom": "https://your-target-site.com"  # Замените на ваш сайт
}

# =============================================================================
# ФУНКЦИИ ВАЛИДАЦИИ
# =============================================================================

def validate_config():
    """Проверка корректности конфигурации"""
    errors = []
    
    # Проверка API ключа
    if not CAP_GURU_API_KEY or CAP_GURU_API_KEY == "your_api_key_here":
        errors.append("❌ Не задан API ключ cap.guru")
    
    # Проверка настроек Linken Sphere
    if not LINKEN_SPHERE_CONFIG["profile_id"] or LINKEN_SPHERE_CONFIG["profile_id"] == "your_profile_id":
        errors.append("❌ Не задан ID профиля Linken Sphere")
    
    # Проверка URL
    if not LINKEN_SPHERE_CONFIG["api_url"].startswith("http"):
        errors.append("❌ Некорректный URL API Linken Sphere")
    
    return errors

def print_config_summary():
    """Вывод сводки конфигурации"""
    print("📋 КОНФИГУРАЦИЯ РЕШАТЕЛЯ reCAPTCHA")
    print("=" * 50)
    print(f"🔑 API ключ cap.guru: {CAP_GURU_API_KEY[:10]}...")
    print(f"🌐 Linken Sphere API: {LINKEN_SPHERE_CONFIG['api_url']}")
    print(f"👤 Профиль: {LINKEN_SPHERE_CONFIG['profile_id']}")
    print(f"📸 Скриншоты: {'включены' if SCREENSHOT_CONFIG['enabled'] else 'отключены'}")
    print(f"📋 Логирование: {LOGGING_CONFIG['level']}")
    print("=" * 50)
    
    # Проверка конфигурации
    errors = validate_config()
    if errors:
        print("⚠️ ОШИБКИ КОНФИГУРАЦИИ:")
        for error in errors:
            print(f"  {error}")
        print("=" * 50)
        return False
    else:
        print("✅ Конфигурация корректна")
        print("=" * 50)
        return True

if __name__ == "__main__":
    print_config_summary()
