#!/usr/bin/env python3
"""
Быстрый запуск решателя reCAPTCHA без поиска существующих браузеров
"""

import time
import logging
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Простая настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('quick_start.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class QuickRecaptchaSolver:
    """Быстрый решатель reCAPTCHA с новым браузером"""
    
    def __init__(self):
        logger.info("🚀 Инициализация быстрого решателя reCAPTCHA")
        self.driver = None
        
        # Создаем папку для скриншотов
        os.makedirs("screenshots", exist_ok=True)
        logger.info("📸 Папка screenshots готова")
    
    def start_new_browser(self):
        """Запуск нового браузера Chrome"""
        logger.info("🌐 Запуск нового браузера Chrome...")
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1200,800")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            logger.debug("Создание WebDriver...")
            start_time = time.time()
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Убираем признаки автоматизации
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            creation_time = time.time() - start_time
            logger.info(f"✅ Браузер запущен за {creation_time:.2f}с")
            
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка запуска браузера: {e}")
            return False
    
    def navigate_to_test_page(self):
        """Переход на тестовую страницу с reCAPTCHA"""
        test_url = "https://www.google.com/recaptcha/api2/demo"
        
        logger.info(f"🌐 Переход на тестовую страницу: {test_url}")
        
        try:
            self.driver.get(test_url)
            time.sleep(3)
            
            # Сохраняем скриншот
            screenshot_path = f"screenshots/01_page_loaded_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            current_url = self.driver.current_url
            title = self.driver.title
            
            logger.info(f"📄 Страница загружена: {title}")
            logger.info(f"🔗 URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка загрузки страницы: {e}")
            return False
    
    def find_recaptcha(self):
        """Поиск reCAPTCHA на странице"""
        logger.info("🔍 Поиск reCAPTCHA на странице...")
        
        try:
            # Ищем iframe с reCAPTCHA
            recaptcha_iframe = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='recaptcha']"))
            )
            
            logger.info("✅ reCAPTCHA iframe найден")
            
            # Сохраняем скриншот
            screenshot_path = f"screenshots/02_recaptcha_found_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            return True
            
        except TimeoutException:
            logger.warning("⚠️ reCAPTCHA не найдена на странице")
            return False
        except Exception as e:
            logger.error(f"❌ Ошибка поиска reCAPTCHA: {e}")
            return False
    
    def click_recaptcha_checkbox(self):
        """Клик по чекбоксу reCAPTCHA"""
        logger.info("🖱️ Попытка клика по чекбоксу reCAPTCHA...")
        
        try:
            # Переключаемся в iframe
            recaptcha_iframe = self.driver.find_element(By.CSS_SELECTOR, "iframe[src*='recaptcha']")
            self.driver.switch_to.frame(recaptcha_iframe)
            
            # Ищем чекбокс
            checkbox = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "recaptcha-anchor"))
            )
            
            logger.info("✅ Чекбокс найден, выполняем клик...")
            checkbox.click()
            
            time.sleep(3)
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            # Сохраняем скриншот
            screenshot_path = f"screenshots/03_checkbox_clicked_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            logger.info("✅ Клик по чекбоксу выполнен")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка клика по чекбоксу: {e}")
            self.driver.switch_to.default_content()
            return False
    
    def wait_for_result(self, timeout=30):
        """Ожидание результата решения капчи"""
        logger.info(f"⏳ Ожидание результата в течение {timeout}с...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Проверяем, появилась ли галочка
                recaptcha_iframe = self.driver.find_element(By.CSS_SELECTOR, "iframe[src*='recaptcha']")
                self.driver.switch_to.frame(recaptcha_iframe)
                
                # Ищем элемент с галочкой
                checkmark = self.driver.find_elements(By.CSS_SELECTOR, ".recaptcha-checkbox-checkmark")
                
                if checkmark:
                    logger.info("✅ Капча решена автоматически!")
                    self.driver.switch_to.default_content()
                    
                    # Финальный скриншот
                    screenshot_path = f"screenshots/04_solved_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.info(f"📸 Финальный скриншот: {screenshot_path}")
                    
                    return True
                
                self.driver.switch_to.default_content()
                time.sleep(2)
                
            except Exception:
                time.sleep(2)
                continue
        
        logger.warning("⚠️ Капча не была решена автоматически")
        return False
    
    def run_demo(self):
        """Запуск демонстрации"""
        logger.info("🎬 ЗАПУСК ДЕМОНСТРАЦИИ РЕШАТЕЛЯ reCAPTCHA")
        logger.info("=" * 60)
        
        try:
            # Шаг 1: Запуск браузера
            if not self.start_new_browser():
                return False
            
            # Шаг 2: Переход на тестовую страницу
            if not self.navigate_to_test_page():
                return False
            
            # Шаг 3: Поиск reCAPTCHA
            if not self.find_recaptcha():
                return False
            
            # Шаг 4: Клик по чекбоксу
            if not self.click_recaptcha_checkbox():
                return False
            
            # Шаг 5: Ожидание результата
            success = self.wait_for_result()
            
            if success:
                logger.info("🎉 ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
                print("\n🎉 Капча решена! Проверьте скриншоты в папке screenshots/")
            else:
                logger.info("⚠️ Демонстрация завершена, но капча требует ручного решения")
                print("\n⚠️ Капча требует ручного решения. Браузер остается открытым.")
            
            # Ждем ввод пользователя
            input("\nНажмите Enter для закрытия браузера...")
            
            return success
            
        except Exception as e:
            logger.error(f"💥 Критическая ошибка: {e}")
            return False
        
        finally:
            self.close()
    
    def close(self):
        """Закрытие браузера"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🔚 Браузер закрыт")
            except:
                pass

def main():
    """Основная функция"""
    try:
        solver = QuickRecaptchaSolver()
        solver.run_demo()
        
    except KeyboardInterrupt:
        logger.info("⏹️ Программа прервана пользователем")
    except Exception as e:
        logger.error(f"💥 Критическая ошибка: {e}")

if __name__ == "__main__":
    main()
