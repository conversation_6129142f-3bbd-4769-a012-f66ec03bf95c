#!/usr/bin/env python3
"""
Пример использования решателя reCAPTCHA v2 для Linken Sphere
"""

from improved_anticaptcha_solver import ImprovedAntiCaptchaSolver
import time

def main():
    """Основная функция с примером использования"""
    
    # Настройки (можно также задать в config.py)
    API_KEY = "your_cap_guru_api_key"  # Замените на ваш API ключ cap.guru
    LINKEN_SPHERE_URL = "http://localhost:35000"  # URL API Linken Sphere
    PROFILE_ID = "your_profile_id"  # ID профиля в Linken Sphere
    TARGET_URL = "https://www.google.com/recaptcha/api2/demo"  # Тестовая страница с reCAPTCHA
    
    print("🚀 Запуск решателя reCAPTCHA v2")
    print("=" * 50)
    
    # Создание экземпляра решателя
    solver = ImprovedAntiCaptchaSolver()
    
    try:
        # Автоматическое решение капчи
        print(f"🎯 Целевой сайт: {TARGET_URL}")
        success = solver.solve_page_recaptcha(TARGET_URL)
        
        if success:
            print("✅ Капча успешно решена!")
            print("🎉 Процесс завершен успешно")
        else:
            print("❌ Не удалось решить капчу")
            print("📋 Проверьте логи в файле anticaptcha.log")
            
    except KeyboardInterrupt:
        print("\n⏹️ Процесс прерван пользователем")
    except Exception as e:
        print(f"💥 Произошла ошибка: {e}")
    finally:
        print("🧹 Очистка ресурсов...")
        solver.close()

def step_by_step_example():
    """Пример пошагового использования"""
    
    print("\n" + "=" * 50)
    print("📚 Пример пошагового использования")
    print("=" * 50)
    
    solver = ImprovedAntiCaptchaSolver()
    
    try:
        # Шаг 1: Запуск браузера
        print("1️⃣ Запуск браузера...")
        if not solver.start_browser_profile():
            print("❌ Не удалось запустить браузер")
            return
        
        # Шаг 2: Переход на сайт
        print("2️⃣ Переход на сайт...")
        target_url = "https://www.google.com/recaptcha/api2/demo"
        if not solver.navigate_to_site(target_url):
            print("❌ Не удалось перейти на сайт")
            return
        
        # Шаг 3: Поиск и клик по чекбоксу
        print("3️⃣ Поиск чекбокса reCAPTCHA...")
        if not solver.find_recaptcha_checkbox():
            print("❌ Чекбокс не найден")
            return
        
        # Шаг 4: Получение задания капчи
        print("4️⃣ Получение задания капчи...")
        instruction_text, image_base64 = solver.get_recaptcha_challenge()
        if not instruction_text or not image_base64:
            print("❌ Не удалось получить задание капчи")
            return
        
        print(f"📝 Инструкция: {instruction_text}")
        print(f"🖼️ Изображение получено (размер: {len(image_base64)} символов)")
        
        # Шаг 5: Отправка на решение
        print("5️⃣ Отправка капчи на решение...")
        task_id = solver.solve_captcha_with_cap_guru(instruction_text, image_base64)
        if not task_id:
            print("❌ Не удалось отправить капчу на решение")
            return
        
        print(f"🆔 ID задания: {task_id}")
        
        # Шаг 6: Получение результата
        print("6️⃣ Ожидание результата...")
        coordinates = solver.get_captcha_result(task_id)
        if not coordinates:
            print("❌ Не удалось получить результат")
            return
        
        print(f"📍 Получены координаты: {coordinates}")
        
        # Шаг 7: Клик по координатам
        print("7️⃣ Клик по координатам...")
        if solver.click_coordinates(coordinates):
            print("✅ Капча успешно решена!")
        else:
            print("❌ Не удалось кликнуть по координатам")
        
    except Exception as e:
        print(f"💥 Ошибка: {e}")
    finally:
        solver.close()

def test_configuration():
    """Тест конфигурации"""
    
    print("\n" + "=" * 50)
    print("🔧 Тест конфигурации")
    print("=" * 50)
    
    try:
        from config import (
            CAP_GURU_API_KEY, 
            LINKEN_SPHERE_CONFIG, 
            CAP_GURU_CONFIG,
            RECAPTCHA_SELECTORS
        )
        
        print("✅ Конфигурация успешно загружена")
        print(f"🔑 API ключ cap.guru: {'*' * (len(CAP_GURU_API_KEY) - 4) + CAP_GURU_API_KEY[-4:] if len(CAP_GURU_API_KEY) > 4 else 'НЕ ЗАДАН'}")
        print(f"🌐 URL Linken Sphere: {LINKEN_SPHERE_CONFIG['api_url']}")
        print(f"👤 Profile ID: {LINKEN_SPHERE_CONFIG['profile_id']}")
        print(f"🎯 Базовый URL cap.guru: {CAP_GURU_CONFIG['base_url']}")
        
        # Проверка селекторов
        print("\n📋 Селекторы reCAPTCHA:")
        for key, selector in RECAPTCHA_SELECTORS.items():
            print(f"  {key}: {selector}")
            
    except ImportError as e:
        print(f"❌ Ошибка импорта конфигурации: {e}")
    except Exception as e:
        print(f"💥 Ошибка при проверке конфигурации: {e}")

if __name__ == "__main__":
    # Тест конфигурации
    test_configuration()
    
    # Основной пример
    main()
    
    # Пошаговый пример (раскомментируйте для использования)
    # step_by_step_example()
    
    print("\n🏁 Все примеры завершены") 