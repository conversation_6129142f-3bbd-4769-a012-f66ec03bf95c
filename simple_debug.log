2025-05-31 08:20:49,253 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:20:49,256 - INFO - --- Тест порта 9222 ---
2025-05-31 08:20:49,262 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:20:49,269 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:20:49,272 - DEBUG - Создание WebDriver...
2025-05-31 08:20:49,283 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:20:49,288 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:20:49,866 - DEBUG - chromedriver not found in PATH
2025-05-31 08:20:49,879 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:20:49,888 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:20:49,934 - DEBUG - Output: ""
2025-05-31 08:20:49,939 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:20:49,955 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:20:49,962 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:20:49,970 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:20:49,981 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:20:49,985 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:20:49,988 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:20:49,994 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:20:50,422 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13856 using 0 to output -3
2025-05-31 08:20:50,545 - DEBUG - POST http://localhost:57251/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:20:50,547 - DEBUG - Starting new HTTP connection (1): localhost:57251
2025-05-31 08:22:27,388 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:22:27,389 - INFO - --- Тест порта 9222 ---
2025-05-31 08:22:27,390 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:22:27,391 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:22:27,393 - DEBUG - Создание WebDriver...
2025-05-31 08:22:27,394 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:22:27,398 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:22:27,620 - DEBUG - chromedriver not found in PATH
2025-05-31 08:22:27,621 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:22:27,622 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:22:27,623 - DEBUG - Output: ""
2025-05-31 08:22:27,624 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:22:27,626 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:22:27,627 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:22:27,628 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:22:27,630 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:22:27,637 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:22:27,638 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:22:27,639 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:22:27,809 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 6020 using 0 to output -3
2025-05-31 08:22:28,330 - DEBUG - POST http://localhost:57449/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:22:28,333 - DEBUG - Starting new HTTP connection (1): localhost:57449
2025-05-31 08:22:41,771 - INFO - Тест прерван пользователем
2025-05-31 08:37:17,979 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:37:17,980 - INFO - --- Тест порта 9222 ---
2025-05-31 08:37:17,983 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:37:17,984 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:37:17,986 - DEBUG - Создание WebDriver...
2025-05-31 08:37:17,988 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:37:17,989 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:37:18,437 - DEBUG - chromedriver not found in PATH
2025-05-31 08:37:18,438 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:18,439 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:37:18,440 - DEBUG - Output: ""
2025-05-31 08:37:18,441 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:37:18,442 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:37:18,443 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:37:18,445 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:37:18,446 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:37:18,447 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:18,451 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:18,453 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:18,545 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 11780 using 0 to output -3
2025-05-31 08:37:19,063 - DEBUG - POST http://localhost:58923/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:37:19,066 - DEBUG - Starting new HTTP connection (1): localhost:58923
2025-05-31 08:38:19,861 - DEBUG - http://localhost:58923 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:38:19,863 - DEBUG - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9222\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:38:19,873 - DEBUG - Finished Request
2025-05-31 08:38:21,181 - DEBUG - WebDriverException на порту 9222: Message: session not created: cannot connect to chrome at localhost:9222
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff63fd26f65+78965]
	GetHandleVerifier [0x0x7ff63fd26fc0+79056]
	...
2025-05-31 08:38:21,191 - INFO - Порт 9222 недоступен
2025-05-31 08:38:21,193 - INFO - --- Тест порта 9223 ---
2025-05-31 08:38:21,195 - INFO - Тестирование порта 9223 с таймаутом 15 сек
2025-05-31 08:38:21,201 - DEBUG - Создание Chrome options для порта 9223
2025-05-31 08:38:21,203 - DEBUG - Создание WebDriver...
2025-05-31 08:38:21,205 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:38:21,206 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:38:21,643 - DEBUG - chromedriver not found in PATH
2025-05-31 08:38:21,646 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:38:21,649 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:38:21,651 - DEBUG - Output: ""
2025-05-31 08:38:21,652 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:38:21,654 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:38:21,655 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:38:21,656 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:38:21,658 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:38:21,659 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:38:21,661 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:38:21,665 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:38:21,673 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 2288 using 0 to output -3
2025-05-31 08:38:22,196 - DEBUG - POST http://localhost:59240/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9223', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:38:22,206 - DEBUG - Starting new HTTP connection (1): localhost:59240
2025-05-31 08:39:23,073 - DEBUG - http://localhost:59240 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:39:23,136 - DEBUG - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9223\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:39:23,147 - DEBUG - Finished Request
2025-05-31 08:39:24,167 - DEBUG - WebDriverException на порту 9223: Message: session not created: cannot connect to chrome at localhost:9223
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff63fd26f65+78965]
	GetHandleVerifier [0x0x7ff63fd26fc0+79056]
	...
2025-05-31 08:39:24,258 - INFO - Порт 9223 недоступен
2025-05-31 08:39:24,263 - INFO - --- Тест порта 9224 ---
2025-05-31 08:39:24,269 - INFO - Тестирование порта 9224 с таймаутом 15 сек
2025-05-31 08:39:24,273 - DEBUG - Создание Chrome options для порта 9224
2025-05-31 08:39:24,283 - DEBUG - Создание WebDriver...
2025-05-31 08:39:24,307 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:39:24,397 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:39:24,633 - DEBUG - chromedriver not found in PATH
2025-05-31 08:39:24,717 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:39:24,721 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:39:24,725 - DEBUG - Output: ""
2025-05-31 08:39:24,729 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:39:24,734 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:39:24,738 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:39:24,742 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:39:24,747 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:39:24,751 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:39:24,756 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:39:24,762 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:39:24,770 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13180 using 0 to output -3
2025-05-31 08:39:25,280 - DEBUG - POST http://localhost:59546/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9224', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:39:25,362 - DEBUG - Starting new HTTP connection (1): localhost:59546
2025-05-31 08:40:26,091 - DEBUG - http://localhost:59546 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:40:26,126 - DEBUG - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9224\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:40:26,133 - DEBUG - Finished Request
2025-05-31 08:40:27,145 - DEBUG - WebDriverException на порту 9224: Message: session not created: cannot connect to chrome at localhost:9224
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff63fd26f65+78965]
	GetHandleVerifier [0x0x7ff63fd26fc0+79056]
	...
2025-05-31 08:40:27,191 - INFO - Порт 9224 недоступен
2025-05-31 08:40:27,203 - INFO - --- Тест порта 9225 ---
2025-05-31 08:40:27,210 - INFO - Тестирование порта 9225 с таймаутом 15 сек
2025-05-31 08:40:27,217 - DEBUG - Создание Chrome options для порта 9225
2025-05-31 08:40:27,224 - DEBUG - Создание WebDriver...
2025-05-31 08:40:27,231 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:40:27,238 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:40:27,434 - DEBUG - chromedriver not found in PATH
2025-05-31 08:40:27,468 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:40:27,475 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:40:27,481 - DEBUG - Output: ""
2025-05-31 08:40:27,486 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:40:27,493 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:40:27,499 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:40:27,505 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:40:27,510 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:40:27,516 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:40:27,522 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:40:27,528 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:40:27,538 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 14200 using 0 to output -3
2025-05-31 08:40:28,061 - DEBUG - POST http://localhost:59733/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9225', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:40:28,133 - DEBUG - Starting new HTTP connection (1): localhost:59733
2025-05-31 08:41:28,975 - DEBUG - http://localhost:59733 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:41:28,999 - DEBUG - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9225\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:41:29,006 - DEBUG - Finished Request
2025-05-31 08:41:30,020 - DEBUG - WebDriverException на порту 9225: Message: session not created: cannot connect to chrome at localhost:9225
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff63fd26f65+78965]
	GetHandleVerifier [0x0x7ff63fd26fc0+79056]
	...
2025-05-31 08:41:30,036 - INFO - Порт 9225 недоступен
2025-05-31 08:41:30,040 - INFO - --- Тест нового браузера ---
2025-05-31 08:41:30,045 - INFO - Запуск нового браузера Chrome
2025-05-31 08:41:30,049 - DEBUG - Создание нового WebDriver...
2025-05-31 08:41:30,053 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:41:30,057 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:41:30,251 - DEBUG - chromedriver not found in PATH
2025-05-31 08:41:30,276 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:41:30,281 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:41:30,285 - DEBUG - Output: ""
2025-05-31 08:41:30,289 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:41:30,294 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:41:30,298 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:41:30,302 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:41:30,307 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:41:30,311 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:41:30,315 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:41:30,319 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:41:30,327 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 8168 using 0 to output -3
2025-05-31 08:41:30,837 - DEBUG - POST http://localhost:59921/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--window-size=1200,800', '--disable-extensions']}}}}
2025-05-31 08:41:30,914 - DEBUG - Starting new HTTP connection (1): localhost:59921
2025-05-31 08:41:35,117 - DEBUG - http://localhost:59921 "POST /session HTTP/1.1" 200 866
2025-05-31 08:41:35,130 - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.41","chrome":{"chromedriverVersion":"137.0.7151.55 (254bc711794d7ad269495f3d419a209935b78cad-refs/branch-heads/7151@{#1757})","userDataDir":"C:\\Windows\\SystemTemp\\scoped_dir8168_1962305702"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:59928"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"70ad55ba1d147cb76d8cd6b379d8b9cd"}} | headers=HTTPHeaderDict({'Content-Length': '866', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:41:35,144 - DEBUG - Finished Request
2025-05-31 08:41:35,149 - DEBUG - Новый WebDriver создан за 5.10 сек
2025-05-31 08:41:35,154 - DEBUG - POST http://localhost:59921/session/70ad55ba1d147cb76d8cd6b379d8b9cd/timeouts {'pageLoad': 10000}
2025-05-31 08:41:35,166 - DEBUG - http://localhost:59921 "POST /session/70ad55ba1d147cb76d8cd6b379d8b9cd/timeouts HTTP/1.1" 200 14
2025-05-31 08:41:35,171 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:41:35,181 - DEBUG - Finished Request
2025-05-31 08:41:35,188 - INFO - УСПЕХ: Новый браузер запущен
2025-05-31 08:41:35,197 - INFO - Новый браузер работает! Тестируем навигацию...
2025-05-31 08:41:35,202 - DEBUG - POST http://localhost:59921/session/70ad55ba1d147cb76d8cd6b379d8b9cd/url {'url': 'https://www.google.com'}
2025-05-31 08:41:36,785 - DEBUG - http://localhost:59921 "POST /session/70ad55ba1d147cb76d8cd6b379d8b9cd/url HTTP/1.1" 200 14
2025-05-31 08:41:36,854 - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:41:36,960 - DEBUG - Finished Request
2025-05-31 08:41:36,967 - DEBUG - GET http://localhost:59921/session/70ad55ba1d147cb76d8cd6b379d8b9cd/title {}
2025-05-31 08:41:36,983 - DEBUG - http://localhost:59921 "GET /session/70ad55ba1d147cb76d8cd6b379d8b9cd/title HTTP/1.1" 200 18
2025-05-31 08:41:36,987 - DEBUG - Remote response: status=200 | data={"value":"Google"} | headers=HTTPHeaderDict({'Content-Length': '18', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:41:36,991 - DEBUG - Finished Request
2025-05-31 08:41:36,995 - INFO - Навигация успешна. Заголовок: Google
2025-05-31 08:58:38,540 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:58:38,624 - INFO - --- Тест порта 9222 ---
2025-05-31 08:58:38,628 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:58:38,632 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:58:38,637 - DEBUG - Создание WebDriver...
2025-05-31 08:58:38,643 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:58:38,649 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:58:38,914 - DEBUG - chromedriver not found in PATH
2025-05-31 08:58:38,950 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:58:38,954 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:58:38,959 - DEBUG - Output: ""
2025-05-31 08:58:38,963 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:58:38,967 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:58:38,971 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:58:38,975 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:58:38,979 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:58:38,983 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:58:38,987 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:58:38,992 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:58:39,093 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 12436 using 0 to output -3
2025-05-31 08:58:39,616 - DEBUG - POST http://localhost:62027/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:58:39,624 - DEBUG - Starting new HTTP connection (1): localhost:62027
