2025-05-31 08:20:49,253 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:20:49,256 - INFO - --- Тест порта 9222 ---
2025-05-31 08:20:49,262 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:20:49,269 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:20:49,272 - DEBUG - Создание WebDriver...
2025-05-31 08:20:49,283 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:20:49,288 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:20:49,866 - DEBUG - chromedriver not found in PATH
2025-05-31 08:20:49,879 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:20:49,888 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:20:49,934 - DEBUG - Output: ""
2025-05-31 08:20:49,939 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:20:49,955 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:20:49,962 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:20:49,970 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:20:49,981 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:20:49,985 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:20:49,988 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:20:49,994 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:20:50,422 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13856 using 0 to output -3
2025-05-31 08:20:50,545 - DEBUG - POST http://localhost:57251/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:20:50,547 - DEBUG - Starting new HTTP connection (1): localhost:57251
2025-05-31 08:22:27,388 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:22:27,389 - INFO - --- Тест порта 9222 ---
2025-05-31 08:22:27,390 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:22:27,391 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:22:27,393 - DEBUG - Создание WebDriver...
2025-05-31 08:22:27,394 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:22:27,398 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:22:27,620 - DEBUG - chromedriver not found in PATH
2025-05-31 08:22:27,621 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:22:27,622 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:22:27,623 - DEBUG - Output: ""
2025-05-31 08:22:27,624 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:22:27,626 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:22:27,627 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:22:27,628 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:22:27,630 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:22:27,637 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:22:27,638 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:22:27,639 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:22:27,809 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 6020 using 0 to output -3
2025-05-31 08:22:28,330 - DEBUG - POST http://localhost:57449/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:22:28,333 - DEBUG - Starting new HTTP connection (1): localhost:57449
2025-05-31 08:22:41,771 - INFO - Тест прерван пользователем
2025-05-31 08:37:17,979 - INFO - === НАЧАЛО ДИАГНОСТИКИ ===
2025-05-31 08:37:17,980 - INFO - --- Тест порта 9222 ---
2025-05-31 08:37:17,983 - INFO - Тестирование порта 9222 с таймаутом 15 сек
2025-05-31 08:37:17,984 - DEBUG - Создание Chrome options для порта 9222
2025-05-31 08:37:17,986 - DEBUG - Создание WebDriver...
2025-05-31 08:37:17,988 - DEBUG - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:37:17,989 - DEBUG - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:37:18,437 - DEBUG - chromedriver not found in PATH
2025-05-31 08:37:18,438 - DEBUG - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:18,439 - DEBUG - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:37:18,440 - DEBUG - Output: ""
2025-05-31 08:37:18,441 - DEBUG - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:37:18,442 - DEBUG - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:37:18,443 - DEBUG - Detected browser: chrome 137.0.7151.41
2025-05-31 08:37:18,445 - DEBUG - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:37:18,446 - DEBUG - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:37:18,447 - DEBUG - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:18,451 - DEBUG - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:18,453 - DEBUG - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:18,545 - DEBUG - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 11780 using 0 to output -3
2025-05-31 08:37:19,063 - DEBUG - POST http://localhost:58923/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']}}}}
2025-05-31 08:37:19,066 - DEBUG - Starting new HTTP connection (1): localhost:58923
