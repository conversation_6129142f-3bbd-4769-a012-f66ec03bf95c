# Конфигурация для решателя reCAPTCHA

# API ключ для сервиса cap.guru
CAP_GURU_API_KEY = "47ff86fb043f86d6c563dca6ffbc2328"

# Настройки Linken Sphere
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:50325",  # Обновленный порт API
    "profile_id": "Desktop preset 1",  # ID профиля из Linken Sphere
    "timeout": 30,  # Таймаут для запросов в секундах
}

# Настройки cap.guru
CAP_GURU_CONFIG = {
    "base_url": "http://api.cap.guru",
    "timeout": 30,  # Таймаут для запросов
    "max_attempts": 30,  # Максимальное количество попыток получения результата
    "check_interval": 5,  # Интервал проверки результата в секундах
}

# Настройки Selenium
SELENIUM_CONFIG = {
    "implicit_wait": 10,  # Неявное ожидание элементов
    "page_load_timeout": 30,  # Таймаут загрузки страницы
    "script_timeout": 30,  # Таймаут выполнения скриптов
}

# Селекторы для reCAPTCHA элементов
RECAPTCHA_SELECTORS = {
    "main_iframe": "iframe[src*='recaptcha']",
    "challenge_iframe": "iframe[src*='bframe']",
    "checkbox": "#recaptcha-anchor",
    "instruction": ".rc-imageselect-desc-no-canonical, .rc-imageselect-desc",
    "image": ".rc-image-tile-wrapper img, .rc-imageselect-payload img",
    "image_area": ".rc-imageselect-payload, .rc-image-tile-wrapper",
    "verify_button": "#recaptcha-verify-button",
    "reload_button": "#recaptcha-reload-button",
}

# Дополнительные настройки
ADDITIONAL_CONFIG = {
    "debug_mode": True,  # Режим отладки
    "save_screenshots": True,  # Сохранять скриншоты для отладки
    "screenshot_dir": "./screenshots",  # Папка для скриншотов
    "log_level": "INFO",  # Уровень логирования
} 