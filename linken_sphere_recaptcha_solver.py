#!/usr/bin/env python3
"""
УЛУЧШЕННЫЙ РЕШАТЕЛЬ reCAPTCHA v2 с полной интеграцией Linken Sphere API + cap.guru
Версия 2.1 - Улучшенное логирование и стабильность

Особенности:
- Автоматический запуск профилей через Linken Sphere API
- Получение WebSocket endpoint из API ответа
- Координатное решение reCAPTCHA через cap.guru
- Полное логирование и скриншоты
- Автоматическое переподключение при сбоях
- Обработка всех типов ошибок

Следует инструкции: https://docs.cap.guru/ru/apiclick/recap.html
"""

import time
import base64
import requests
import json
import os
import logging
import socket
import urllib.parse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from PIL import Image
import io

# Настройка расширенного логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('linken_sphere_api_solver.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class AdvancedLinkenSphereRecaptchaSolver:
    """Улучшенный решатель reCAPTCHA v2 с полной API интеграцией Linken Sphere"""

    def __init__(self, cap_guru_api_key, linken_sphere_api_url, profile_id):
        """
        Инициализация решателя

        Args:
            cap_guru_api_key (str): API ключ для cap.guru
            linken_sphere_api_url (str): URL API Linken Sphere
            profile_id (str): ID профиля в Linken Sphere
        """
        self.cap_guru_api_key = cap_guru_api_key
        self.linken_sphere_api_url = linken_sphere_api_url.rstrip('/')
        self.profile_id = profile_id
        self.driver = None
        self.browser_port = None
        self.session_id = None
        self.ws_endpoint = None

        # Настройки cap.guru API
        self.cap_guru_base_url = "http://api.cap.guru"
        self.max_attempts = 30
        self.check_interval = 5

        # Настройки API запросов
        self.api_timeout = 30
        self.retry_attempts = 3

        # Создаем папку для скриншотов
        self.screenshot_dir = "screenshots"
        os.makedirs(self.screenshot_dir, exist_ok=True)

        logger.info("=" * 60)
        logger.info("INIT: Advanced Linken Sphere Solver Initialized")
        logger.info("=" * 60)
        logger.info(f"CAP.GURU API Key: {cap_guru_api_key[:10]}...")
        logger.info(f"Linken Sphere API URL: {linken_sphere_api_url}")
        logger.info(f"Linken Sphere Profile ID: {profile_id}")
        logger.info(f"Screenshot Directory: ./{self.screenshot_dir}")
        logger.info("=" * 60)

    def _save_screenshot(self, name_prefix):
        """Сохранение скриншота с уникальным именем"""
        try:
            if self.driver:
                timestamp = int(time.time())
                filename = f"{name_prefix}_{timestamp}.png"
                path = os.path.join(self.screenshot_dir, filename)
                self.driver.save_screenshot(path)
                logger.info(f"SCREENSHOT: Saved to {path}")
        except Exception as e:
            logger.error(f"Failed to save screenshot: {e}")

    def is_port_open(self, host='localhost', port=9222, timeout=1):
        """Проверка доступности порта"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(timeout)
                result = sock.connect_ex((host, port))
                return result == 0
        except Exception as e:
            logger.debug(f"Port check error for {host}:{port} - {e}")
            return False
    
    def discover_api_endpoints(self):
        """Автоматическое обнаружение рабочих API эндпоинтов Linken Sphere"""
        logger.info("API_DISCOVERY: Starting Linken Sphere API endpoint discovery...")

        endpoints_to_test = [
            "/sessions/start", "/api/sessions/start", "/api/v1/sessions/start",
            "/profiles/start", "/api/profiles/start", "/browser/start", "/api/browser/start"
        ]
        test_data_variants = [
            {"profile_id": self.profile_id}, {"profile_name": self.profile_id},
            {"name": self.profile_id}, {"profileId": self.profile_id}, {"id": self.profile_id}
        ]

        for endpoint in endpoints_to_test:
            url = f"{self.linken_sphere_api_url}{endpoint}"
            logger.debug(f"API_DISCOVERY: Testing endpoint: POST {url}")
            for data_payload in test_data_variants:
                try:
                    response = requests.post(url, json=data_payload, timeout=5)
                    logger.debug(f"API_DISCOVERY: POST {url} with {data_payload} | Status: {response.status_code}")
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if (result.get("status") == "success" or result.get("success") is True or
                                "port" in result or "ws_endpoint" in result or "session_id" in result):
                                logger.info(f"API_DISCOVERY: Found working endpoint: {endpoint} with data: {data_payload}")
                                return endpoint, data_payload
                        except json.JSONDecodeError:
                            logger.debug(f"API_DISCOVERY: JSONDecodeError for {url} with {data_payload}")
                            pass # Not a valid JSON response
                    elif response.status_code == 400: # Endpoint exists, but data is wrong for this specific variant
                        logger.debug(f"API_DISCOVERY: Endpoint {endpoint} exists (HTTP 400), trying other data variants.")
                        continue # Try next data variant for this endpoint
                except requests.exceptions.RequestException as e:
                    logger.debug(f"API_DISCOVERY: RequestException for {url} with {data_payload}: {e}")
                    # This endpoint (or server) is likely not available, try next endpoint
                    break # Break from data_variants loop, try next endpoint
        
        logger.warning("API_DISCOVERY: No working Linken Sphere API endpoints found through auto-discovery.")
        return None, None

    def start_browser_profile(self):
        """Запуск браузера через API Linken Sphere с автообнаружением"""
        logger.info("PROFILE_START: Attempting to start browser via Linken Sphere API...")

        working_endpoint, working_data = self.discover_api_endpoints()
        if working_endpoint and working_data:
            if self._start_profile_with_endpoint(working_endpoint, working_data):
                return True
            else:
                logger.warning("PROFILE_START: Auto-discovered endpoint failed. Falling back to standard variants.")
        else:
            logger.info("PROFILE_START: Auto-discovery failed or skipped. Trying standard API variants.")

        return self._try_standard_api_variants()

    def _start_profile_with_endpoint(self, endpoint, data_payload):
        """Запуск профиля с конкретным эндпоинтом и данными"""
        url = f"{self.linken_sphere_api_url}{endpoint}"
        try:
            logger.info(f"PROFILE_API: Sending request to start profile: POST {url}")
            logger.debug(f"PROFILE_API: Request data: {data_payload}")
            response = requests.post(url, json=data_payload, timeout=self.api_timeout)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"PROFILE_API: Profile start successful (HTTP 200)")
                logger.debug(f"PROFILE_API: Response JSON: {result}")

                self.session_id = result.get("session_id") or result.get("id")
                self.browser_port = (result.get("port") or result.get("debug_port") or
                                   result.get("debugger_port") or result.get("automation_port") or result.get("debugPort"))
                self.ws_endpoint = result.get("ws_endpoint") or result.get("websocket_url") or result.get("webSocketDebuggerUrl")

                if self.browser_port:
                    logger.info(f"PROFILE_API: Browser debug port received: {self.browser_port}")
                    if self.session_id: logger.info(f"PROFILE_API: Session ID: {self.session_id}")
                    if self.ws_endpoint: logger.info(f"PROFILE_API: WebSocket Endpoint: {self.ws_endpoint}")
                    return self._connect_to_browser()
                else:
                    logger.warning("PROFILE_API: Profile started, but no debug port information received in API response.")
                    return self._try_standard_ports() # Fallback to standard ports
            else:
                logger.error(f"PROFILE_API: Failed to start profile. HTTP Status: {response.status_code}")
                logger.error(f"PROFILE_API: Response Text: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"PROFILE_API: RequestException while starting profile at {url}: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"PROFILE_API: JSONDecodeError from response of {url}: {e} - Response text: {response.text}")
            return False

    def _connect_to_browser(self):
        """Подключение к браузеру через DevTools Protocol"""
        if not self.browser_port:
            logger.error("BROWSER_CONNECT: Cannot connect, browser_port is not set.")
            return False
            
        logger.info(f"BROWSER_CONNECT: Attempting to connect to browser on 127.0.0.1:{self.browser_port}...")
        try:
            if not self.is_port_open('127.0.0.1', int(self.browser_port)):
                logger.error(f"BROWSER_CONNECT: Port 127.0.0.1:{self.browser_port} is not open.")
                # Try to parse ws_endpoint for port if regular port fails or not available
                if self.ws_endpoint:
                    try:
                        parsed_url = urllib.parse.urlparse(self.ws_endpoint)
                        ws_port = parsed_url.port
                        if ws_port and self.is_port_open(parsed_url.hostname, int(ws_port)):
                            logger.info(f"BROWSER_CONNECT: Using WebSocket port {ws_port} from endpoint {self.ws_endpoint}")
                            self.browser_port = ws_port # Update browser port if WebSocket port works
                        else:
                            logger.error(f"BROWSER_CONNECT: WebSocket port {ws_port} from {self.ws_endpoint} also not open or invalid.")
                            return False
                    except Exception as e_ws:
                        logger.error(f"BROWSER_CONNECT: Error parsing WebSocket endpoint or checking its port: {e_ws}")
                        return False
                else:
                    return False


            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.browser_port}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            # chrome_options.add_argument("--disable-gpu") # Often not needed for remote, can cause issues
            
            logger.debug("BROWSER_CONNECT: Initializing WebDriver with options...")
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(45) # Increased timeout
            self.driver.implicitly_wait(15)    # Increased implicit wait

            current_url = self.driver.current_url
            logger.info(f"BROWSER_CONNECT: Successfully connected to browser!")
            logger.info(f"BROWSER_CONNECT: Current Page URL: {current_url}, Title: {self.driver.title}")
            self._save_screenshot("00_browser_connected")
            return True
        except WebDriverException as e:
            logger.error(f"BROWSER_CONNECT: WebDriverException: {e}")
            if "chrome not reachable" in str(e).lower() or "cannot connect" in str(e).lower():
                logger.error(f"BROWSER_CONNECT: Ensure Linken Sphere profile is running and debugger port {self.browser_port} is correct and accessible.")
            if self.driver:
                try: self.driver.quit()
                except: pass
            self.driver = None
            return False
        except Exception as e:
            logger.error(f"BROWSER_CONNECT: Unexpected error connecting to browser: {e}")
            if self.driver:
                try: self.driver.quit()
                except: pass
            self.driver = None
            return False

    def _try_standard_api_variants(self):
        """Попытка запуска через стандартные варианты API Linken Sphere"""
        logger.info("API_FALLBACK: Trying standard API variants to start profile...")
        api_variants = [
            ("/sessions/start", {"profile_id": self.profile_id}),
            ("/api/v1/sessions/start", {"profile_id": self.profile_id}),
            ("/profiles/start", {"profile_id": self.profile_id}), # Common variant
            ("/profiles/start", {"id": self.profile_id}),
        ]
        for endpoint, data in api_variants:
            logger.debug(f"API_FALLBACK: Trying endpoint: {endpoint} with data: {data}")
            if self._start_profile_with_endpoint(endpoint, data):
                return True
        
        logger.warning("API_FALLBACK: All standard API variants failed. Attempting connection to standard debug ports.")
        return self._try_standard_ports()

    def _try_standard_ports(self):
        """Попытка подключения к стандартным портам отладки (если API не вернул порт)"""
        logger.info("PORT_FALLBACK: Attempting to connect to standard debug ports (9222-9228).")
        standard_ports = [9222, 9223, 9224, 9225, 9226, 9227, 9228]
        for port in standard_ports:
            logger.debug(f"PORT_FALLBACK: Checking port {port}...")
            if self.is_port_open('127.0.0.1', port):
                logger.info(f"PORT_FALLBACK: Port {port} is open. Attempting connection.")
                self.browser_port = port # Set port before attempting connection
                if self._connect_to_browser():
                    return True
            else:
                logger.debug(f"PORT_FALLBACK: Port {port} is closed or not responding.")
        
        logger.error("PORT_FALLBACK: Failed to connect to any standard debug ports.")
        return False

    def stop_browser_profile(self):
        """Остановка профиля через API Linken Sphere"""
        if not self.session_id and not self.profile_id:
            logger.warning("PROFILE_STOP: No session_id or profile_id available. Skipping API stop.")
            return True
        
        target_id = self.session_id or self.profile_id
        id_type = "session_id" if self.session_id else "profile_id"
        logger.info(f"PROFILE_STOP: Attempting to stop profile via API ({id_type}: {target_id})")

        stop_endpoints = [
            "/sessions/stop", "/api/sessions/stop", "/api/v1/sessions/stop",
            "/profiles/stop", "/api/profiles/stop", "/browser/stop"
        ]
        # Data payloads will vary based on what ID we have
        stop_data_variants = []
        if self.session_id:
            stop_data_variants.append({"session_id": self.session_id})
            stop_data_variants.append({"id": self.session_id}) # some APIs might use "id" for session
        if self.profile_id:
            stop_data_variants.append({"profile_id": self.profile_id})
            stop_data_variants.append({"id": self.profile_id}) # some APIs might use "id" for profile

        if not stop_data_variants: # Should not happen if one of IDs is present
             logger.warning("PROFILE_STOP: No valid data payload for stopping profile.")
             return True


        for endpoint in stop_endpoints:
            for data_payload in stop_data_variants:
                # Ensure data_payload is relevant for the id we are primarily using
                if (("session_id" in data_payload or ("id" in data_payload and self.session_id)) and not self.session_id) or \
                   (("profile_id" in data_payload or ("id" in data_payload and self.profile_id and not self.session_id)) and not self.profile_id):
                    continue # Skip payload if it's for an ID we don't have

                url = f"{self.linken_sphere_api_url}{endpoint}"
                try:
                    logger.debug(f"PROFILE_STOP: Trying POST {url} with data: {data_payload}")
                    response = requests.post(url, json=data_payload, timeout=self.api_timeout)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get("status") == "success" or result.get("success") is True:
                                logger.info(f"PROFILE_STOP: Profile stopped successfully via {endpoint}.")
                                return True
                            else:
                                logger.debug(f"PROFILE_STOP: Stop command to {endpoint} returned success=false or no status. Response: {result}")
                        except json.JSONDecodeError:
                             logger.debug(f"PROFILE_STOP: Stop command to {endpoint} returned non-JSON response: {response.text}")
                    else:
                        logger.debug(f"PROFILE_STOP: Stop command to {endpoint} failed. HTTP {response.status_code}, Text: {response.text}")
                except requests.exceptions.RequestException as e:
                    logger.debug(f"PROFILE_STOP: RequestException for {url}: {e}")
                    break # If one endpoint fails with request exception, likely server issue, try next endpoint type
        
        logger.warning(f"PROFILE_STOP: Failed to stop profile {target_id} via API using available endpoints.")
        return True # Non-critical, browser might close on driver.quit()

    def close(self):
        """Закрытие браузера и очистка ресурсов"""
        logger.info("CLOSE_RESOURCES: Closing browser and cleaning up resources...")
        try:
            if self.driver:
                logger.info("CLOSE_RESOURCES: Quitting WebDriver...")
                self.driver.quit()
                logger.info("CLOSE_RESOURCES: WebDriver quit successfully.")
        except Exception as e:
            logger.error(f"CLOSE_RESOURCES: Error quitting WebDriver: {e}")
        
        self.stop_browser_profile()
        
        self.driver = None
        self.browser_port = None
        self.session_id = None
        self.ws_endpoint = None
        logger.info("CLOSE_RESOURCES: Cleanup complete.")

    def solve_recaptcha_full_cycle(self, target_url):
        """Полный цикл решения reCAPTCHA"""
        logger.info("=" * 60)
        logger.info(f"RECAPTCHA_CYCLE: Starting full reCAPTCHA solving cycle for URL: {target_url}")
        logger.info("=" * 60)
        try:
            if not self.start_browser_profile():
                logger.error("RECAPTCHA_CYCLE: Failed to start browser profile. Aborting.")
                return False
            if not self.navigate_to_site(target_url):
                logger.error("RECAPTCHA_CYCLE: Failed to navigate to target site. Aborting.")
                return False
            if not self.find_recaptcha_checkbox():
                logger.error("RECAPTCHA_CYCLE: Failed to find and click reCAPTCHA checkbox. Aborting.")
                return False
            
            # Wait for challenge to appear after checkbox click
            logger.info("RECAPTCHA_CYCLE: Waiting for reCAPTCHA challenge to load...")
            time.sleep(5) # Allow time for challenge iframe to load after checkbox click

            instruction, image_base64 = self.get_recaptcha_challenge()
            if not instruction or not image_base64:
                logger.error("RECAPTCHA_CYCLE: Failed to get reCAPTCHA challenge. Aborting.")
                return False
            task_id = self.solve_captcha_with_cap_guru(instruction, image_base64)
            if not task_id:
                logger.error("RECAPTCHA_CYCLE: Failed to submit challenge to cap.guru. Aborting.")
                return False
            coordinates = self.get_captcha_result(task_id)
            if not coordinates:
                logger.error("RECAPTCHA_CYCLE: Failed to get coordinates from cap.guru. Aborting.")
                return False
            if not self.click_coordinates(coordinates):
                logger.error("RECAPTCHA_CYCLE: Failed to click coordinates. Aborting.")
                return False
            if self.verify_captcha_solved():
                logger.info("RECAPTCHA_CYCLE: CAPTCHA SOLVED SUCCESSFULLY!")
                self._save_screenshot("99_captcha_solved_final")
                return True
            else:
                logger.warning("RECAPTCHA_CYCLE: CAPTCHA challenge submitted, but verification failed.")
                self._save_screenshot("98_captcha_verification_failed")
                return False
        except Exception as e:
            logger.error(f"RECAPTCHA_CYCLE: Critical error during solving cycle: {e}", exc_info=True)
            self._save_screenshot("97_critical_error_in_cycle")
            return False
        finally:
            self.close()
            logger.info("RECAPTCHA_CYCLE: Full cycle finished.")

    def navigate_to_site(self, url):
        """Переход на сайт с капчей"""
        if not self.driver:
            logger.error("NAVIGATE: WebDriver not available.")
            return False
        logger.info(f"NAVIGATE: Navigating to URL: {url}")
        try:
            self.driver.get(url)
            WebDriverWait(self.driver, 30).until(EC.url_contains(urllib.parse.urlparse(url).hostname or url[:50]))
            logger.info(f"NAVIGATE: Page loaded. Current URL: {self.driver.current_url}, Title: {self.driver.title}")
            self._save_screenshot("01_site_loaded")
            return True
        except TimeoutException:
            logger.error(f"NAVIGATE: TimeoutException while loading URL: {url}. Current URL: {self.driver.current_url}")
            self._save_screenshot("01_site_load_timeout")
            return False
        except Exception as e:
            logger.error(f"NAVIGATE: Error navigating to {url}: {e}")
            self._save_screenshot("01_site_load_error")
            return False
    
    def find_recaptcha_checkbox(self):
        """Поиск и клик по чекбоксу reCAPTCHA"""
        if not self.driver:
            logger.error("CHECKBOX_FIND: WebDriver not available.")
            return False
        logger.info("CHECKBOX_FIND: Searching for reCAPTCHA checkbox...")
        try:
            # Wait for the main reCAPTCHA iframe (anchor iframe)
            WebDriverWait(self.driver, 20).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "iframe[src*='recaptcha'][src*='anchor']"))
            )
            logger.info("CHECKBOX_FIND: Switched to reCAPTCHA anchor iframe.")
            self._save_screenshot("02a_recaptcha_anchor_iframe")

            #Locators for the checkbox
            checkbox_selectors = [
                (By.ID, "recaptcha-anchor"),
                (By.CSS_SELECTOR, ".recaptcha-checkbox-border"),
                (By.CSS_SELECTOR, "div.recaptcha-checkbox-checkmark"),
                (By.XPATH, "//div[@role='checkbox']")
            ]
            checkbox = None
            for by_method, selector_value in checkbox_selectors:
                try:
                    logger.debug(f"CHECKBOX_FIND: Trying selector {by_method} : {selector_value}")
                    # It's important for the checkbox to be clickable
                    checkbox = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((by_method, selector_value))
                    )
                    if checkbox:
                        logger.info(f"CHECKBOX_FIND: reCAPTCHA checkbox found and clickable using {by_method}: {selector_value}.")
                        break
                except TimeoutException:
                    logger.debug(f"CHECKBOX_FIND: Timeout for selector {by_method} : {selector_value}")
                    continue
            
            if not checkbox:
                logger.error("CHECKBOX_FIND: reCAPTCHA checkbox not found or not clickable with any known selectors.")
                self._save_screenshot("02b_checkbox_not_found")
                self.driver.switch_to.default_content()
                return False

            logger.info("CHECKBOX_FIND: Clicking reCAPTCHA checkbox...")
            # Added move_to_element before click for reliability
            ActionChains(self.driver).move_to_element(checkbox).click().perform()
            # checkbox.click() # Simpler click if ActionChains has issues

            logger.info("CHECKBOX_FIND: reCAPTCHA checkbox clicked.")
            self.driver.switch_to.default_content()
            self._save_screenshot("03_checkbox_clicked")
            return True

        except TimeoutException:
            logger.error("CHECKBOX_FIND: Timeout waiting for reCAPTCHA anchor iframe or checkbox.")
            self._save_screenshot("02c_checkbox_timeout")
            try: self.driver.switch_to.default_content()
            except: pass
            return False
        except Exception as e:
            logger.error(f"CHECKBOX_FIND: Error finding or clicking reCAPTCHA checkbox: {e}", exc_info=True)
            self._save_screenshot("02d_checkbox_error")
            try: self.driver.switch_to.default_content()
            except: pass
            return False
    
    def get_recaptcha_challenge(self):
        """Получение задания капчи (изображение и инструкция)"""
        if not self.driver:
            logger.error("CHALLENGE_GET: WebDriver not available.")
            return None, None
        logger.info("CHALLENGE_GET: Getting reCAPTCHA challenge (image and instructions)...")
        try:
            # Wait for the challenge iframe (bframe)
            # This iframe appears *after* the checkbox is clicked and verification is triggered
            WebDriverWait(self.driver, 20).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "iframe[src*='recaptcha'][src*='bframe']"))
            )
            logger.info("CHALLENGE_GET: Switched to reCAPTCHA challenge (bframe) iframe.")
            self._save_screenshot("04a_challenge_bframe_iframe")

            # Get instruction text
            instruction_selectors = [
                (By.CSS_SELECTOR, ".rc-imageselect-instructions strong"), # Often the main text is in strong
                (By.CSS_SELECTOR, ".rc-imageselect-instructions"),
                (By.CSS_SELECTOR, "div.rc-imageselect-desc-no-canonical"),
                (By.CSS_SELECTOR, "div.rc-imageselect-desc"),
                (By.XPATH, "//*[contains(@class, 'instructions') or contains(@class, 'prompt')]")
            ]
            instruction_text = None
            for by_method, selector_value in instruction_selectors:
                try:
                    instruction_element = WebDriverWait(self.driver, 5).until(
                        EC.visibility_of_element_located((by_method, selector_value))
                    )
                    instruction_text = instruction_element.text.strip()
                    if instruction_text:
                        logger.info(f"CHALLENGE_GET: Instruction found: '{instruction_text}' using {by_method}: {selector_value}")
                        break
                except TimeoutException:
                    logger.debug(f"CHALLENGE_GET: Instruction not found with {by_method}: {selector_value}")
                    continue
            
            if not instruction_text:
                logger.error("CHALLENGE_GET: Failed to find instruction text within challenge iframe.")
                self._save_screenshot("04b_instruction_not_found")
                self.driver.switch_to.default_content()
                return None, None

            # Get challenge image (typically a 3x3 or 4x4 grid)
            # The image for cap.guru is the entire grid of selectable images.
            image_payload_element_selectors = [
                 (By.CSS_SELECTOR, ".rc-imageselect-table-33"), # 3x3 grid
                 (By.CSS_SELECTOR, ".rc-imageselect-table-42"), # 4x2 grid
                 (By.CSS_SELECTOR, ".rc-imageselect-table-44"), # 4x4 grid (less common for 'click')
                 (By.CSS_SELECTOR, "table.rc-imageselect-table"), # General table
                 (By.ID, "rc-imageselect-target") # Fallback to a common parent
            ]
            image_element_for_screenshot = None
            for by_method, selector_value in image_payload_element_selectors:
                try:
                    image_element_for_screenshot = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((by_method, selector_value))
                    )
                    if image_element_for_screenshot:
                        logger.info(f"CHALLENGE_GET: Image grid area found for screenshot using {by_method}: {selector_value}")
                        break
                except TimeoutException:
                    continue
            
            if not image_element_for_screenshot:
                logger.warning("CHALLENGE_GET: Could not find specific image grid element, taking screenshot of iframe body.")
                image_element_for_screenshot = self.driver.find_element(By.TAG_NAME, "body")


            # Take screenshot of the image grid element
            image_bytes = image_element_for_screenshot.screenshot_as_png
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            challenge_image_path = os.path.join(self.screenshot_dir, f"05_challenge_image_{int(time.time())}.png")
            with open(challenge_image_path, 'wb') as f:
                f.write(image_bytes)
            logger.info(f"CHALLENGE_GET: Screenshot of challenge image saved to: {challenge_image_path}")

            self.driver.switch_to.default_content()
            logger.info(f"CHALLENGE_GET: Successfully got reCAPTCHA challenge. Instruction: '{instruction_text}', Image size: {len(image_base64)} bytes (base64).")
            return instruction_text, image_base64

        except TimeoutException:
            logger.error("CHALLENGE_GET: Timeout waiting for reCAPTCHA challenge (bframe) iframe or its elements.")
            self._save_screenshot("04c_challenge_timeout")
            try: self.driver.switch_to.default_content()
            except: pass
            return None, None
        except Exception as e:
            logger.error(f"CHALLENGE_GET: Error getting reCAPTCHA challenge: {e}", exc_info=True)
            self._save_screenshot("04d_challenge_error")
            try: self.driver.switch_to.default_content()
            except: pass
            return None, None

    def solve_captcha_with_cap_guru(self, instruction_text, image_base64):
        """Отправка капчи на решение в cap.guru"""
        logger.info("CAPGURU_SUBMIT: Submitting reCAPTCHA challenge to cap.guru...")
        try:
            post_data = {
                'key': self.cap_guru_api_key,
                'method': 'base64',
                'textinstructions': instruction_text, # As per https://docs.cap.guru/ru/apiclick/recap.html
                'body': image_base64
            }
            logger.debug(f"CAPGURU_SUBMIT: POSTing to {self.cap_guru_base_url}/in.php with instruction: '{instruction_text}', image_size: {len(image_base64)}")
            
            response = requests.post(f"{self.cap_guru_base_url}/in.php", data=post_data, timeout=self.api_timeout)

            if response.status_code != 200:
                logger.error(f"CAPGURU_SUBMIT: HTTP error {response.status_code} submitting to cap.guru. Response: {response.text}")
                return None
            
            result_text = response.text.strip()
            logger.debug(f"CAPGURU_SUBMIT: Response from cap.guru/in.php: {result_text}")
            if result_text.startswith("OK|"):
                task_id = result_text.split("|")[1]
                logger.info(f"CAPGURU_SUBMIT: Task created successfully with ID: {task_id}")
                return task_id
            else:
                logger.error(f"CAPGURU_SUBMIT: Failed to create task. cap.guru error: {result_text}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"CAPGURU_SUBMIT: RequestException submitting to cap.guru: {e}")
            return None
        except Exception as e:
            logger.error(f"CAPGURU_SUBMIT: Unexpected error: {e}", exc_info=True)
            return None

    def get_captcha_result(self, task_id):
        """Получение результата решения капчи от cap.guru"""
        logger.info(f"CAPGURU_RESULT: Waiting for cap.guru result for task ID: {task_id}...")
        for attempt in range(self.max_attempts):
            try:
                logger.debug(f"CAPGURU_RESULT: Attempt {attempt + 1}/{self.max_attempts}. Waiting {self.check_interval}s...")
                time.sleep(self.check_interval)
                params = {
                    'key': self.cap_guru_api_key,
                    'action': 'get',
                    'id': task_id
                }
                response = requests.get(f"{self.cap_guru_base_url}/res.php", params=params, timeout=self.api_timeout)

                if response.status_code != 200:
                    logger.warning(f"CAPGURU_RESULT: HTTP error {response.status_code} getting result. Response: {response.text}")
                    continue
                
                result_text = response.text.strip()
                logger.debug(f"CAPGURU_RESULT: Response from cap.guru/res.php: {result_text}")

                if result_text == "CAPCHA_NOT_READY":
                    logger.info(f"CAPGURU_RESULT: CAPCHA_NOT_READY. Retrying...")
                    continue
                elif result_text.startswith("OK|coordinate:"):
                    # Parse coordinates: "OK|coordinate:x=44,y=32;x=12,y=30"
                    coordinates_str_part = result_text.split("coordinate:")[1]
                    coordinates = []
                    if coordinates_str_part: # Ensure it's not empty
                        coord_pairs = coordinates_str_part.split(';')
                        for pair in coord_pairs:
                            if 'x=' in pair and 'y=' in pair:
                                try:
                                    x_val = int(pair.split('x=')[1].split(',')[0])
                                    y_val = int(pair.split('y=')[1])
                                    coordinates.append({'x': x_val, 'y': y_val})
                                except ValueError:
                                    logger.error(f"CAPGURU_RESULT: ValueError parsing coordinate pair: '{pair}'")
                                    continue # Skip malformed pair
                    if coordinates:
                        logger.info(f"CAPGURU_RESULT: Coordinates received: {coordinates}")
                        return coordinates
                    else: # String was "OK|coordinate:" but no actual coordinates
                        logger.error(f"CAPGURU_RESULT: Received 'OK|coordinate:' but no valid coordinates followed: '{coordinates_str_part}'")
                        return None
                else: # Some other error from cap.guru
                    logger.error(f"CAPGURU_RESULT: Failed to get solution. cap.guru error: {result_text}")
                    return None
            except requests.exceptions.RequestException as e:
                logger.warning(f"CAPGURU_RESULT: RequestException getting result (attempt {attempt + 1}): {e}")
                continue # Retry on connection issues
            except Exception as e:
                logger.error(f"CAPGURU_RESULT: Unexpected error getting result (attempt {attempt + 1}): {e}", exc_info=True)
                return None # Fatal error for this attempt

        logger.error(f"CAPGURU_RESULT: Exceeded max attempts ({self.max_attempts}) waiting for solution for task ID {task_id}.")
        return None

    def click_coordinates(self, coordinates):
        """Клик по полученным координатам в iframe капчи"""
        if not self.driver:
            logger.error("COORD_CLICK: WebDriver not available.")
            return False
        if not coordinates:
            logger.error("COORD_CLICK: No coordinates provided to click.")
            return False
            
        logger.info(f"COORD_CLICK: Clicking coordinates: {coordinates}")
        try:
            # Ensure we are in the bframe (challenge iframe)
            WebDriverWait(self.driver, 10).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "iframe[src*='recaptcha'][src*='bframe']"))
            )
            logger.info("COORD_CLICK: Switched to reCAPTCHA challenge (bframe) iframe.")

            # Find the image area to click within. This should be the same element used for screenshotting.
            image_area_selectors = [
                 (By.CSS_SELECTOR, ".rc-imageselect-table-33"), 
                 (By.CSS_SELECTOR, ".rc-imageselect-table-42"), 
                 (By.CSS_SELECTOR, ".rc-imageselect-table-44"), 
                 (By.CSS_SELECTOR, "table.rc-imageselect-table"), 
                 (By.ID, "rc-imageselect-target"),
                 (By.TAG_NAME, "body") # Absolute fallback
            ]
            image_area_element = None
            for by_method, selector_value in image_area_selectors:
                try:
                    image_area_element = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((by_method, selector_value))
                    )
                    if image_area_element:
                        logger.info(f"COORD_CLICK: Image area for clicking found using {by_method}: {selector_value}")
                        break
                except TimeoutException:
                    continue
            
            if not image_area_element:
                logger.error("COORD_CLICK: Could not find image area for clicking. Aborting clicks.")
                self.driver.switch_to.default_content()
                return False

            actions = ActionChains(self.driver)
            for i, coord in enumerate(coordinates):
                x, y = coord['x'], coord['y']
                logger.info(f"COORD_CLICK: Clicking coordinate {i+1}/{len(coordinates)}: x={x}, y={y}")
                # Perform click relative to the top-left of the image_area_element
                actions.move_to_element_with_offset(image_area_element, x, y).click().perform()
                self._save_screenshot(f"06_coord_click_{i+1}")
                time.sleep(0.5 + (i * 0.1)) # Small incremental delay

            logger.info("COORD_CLICK: All coordinates clicked.")
            
            # Click the "Verify" / "Подтвердить" button
            verify_button_selectors = [
                (By.ID, "recaptcha-verify-button"),
                (By.CSS_SELECTOR, ".rc-button-default.rc-button-submit"), # More specific verify
                (By.XPATH, "//button[contains(text(),'Verify') or contains(text(),'Подтвердить')]")
            ]
            verify_button = None
            for by_method, selector_value in verify_button_selectors:
                try:
                    verify_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((by_method, selector_value))
                    )
                    if verify_button:
                        logger.info(f"COORD_CLICK: Verify button found using {by_method}: {selector_value}")
                        break
                except TimeoutException:
                    continue
            
            if verify_button:
                logger.info("COORD_CLICK: Clicking Verify button...")
                verify_button.click()
                self._save_screenshot("07_verify_button_clicked")
            else:
                logger.warning("COORD_CLICK: Verify button not found or not clickable after clicking coordinates.")
                self._save_screenshot("07_verify_button_not_found")

            self.driver.switch_to.default_content()
            logger.info("COORD_CLICK: Click actions complete.")
            return True
        except TimeoutException:
            logger.error("COORD_CLICK: Timeout switching to challenge (bframe) iframe or finding elements.")
            self._save_screenshot("06z_coord_click_timeout")
            try: self.driver.switch_to.default_content()
            except: pass
            return False
        except Exception as e:
            logger.error(f"COORD_CLICK: Error clicking coordinates: {e}", exc_info=True)
            self._save_screenshot("06z_coord_click_error")
            try: self.driver.switch_to.default_content()
            except: pass
            return False

    def verify_captcha_solved(self, timeout=10): # Reduced timeout as success should be quick
        """Проверка успешного решения капчи (после кликов и нажатия Verify)"""
        if not self.driver:
            logger.error("VERIFY_SOLVED: WebDriver not available.")
            return False
        logger.info("VERIFY_SOLVED: Verifying if reCAPTCHA is solved...")
        
        # Give a brief moment for verification to process
        time.sleep(3) # Wait for AJAX or page changes

        # Check for indicators of success *outside* the bframe, typically in the anchor iframe or main page
        try:
            # Attempt to switch to anchor iframe first
            try:
                WebDriverWait(self.driver, 5).until(
                    EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "iframe[src*='recaptcha'][src*='anchor']"))
                )
                logger.debug("VERIFY_SOLVED: Switched to anchor iframe for verification.")
                
                success_indicator = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".recaptcha-checkbox-checked, #recaptcha-anchor[aria-checked='true']"))
                )
                if success_indicator:
                    logger.info("VERIFY_SOLVED: Success! Checkbox in anchor iframe shows as checked.")
                    self.driver.switch_to.default_content()
                    self._save_screenshot("09_captcha_verified_success_anchor")
                    return True
            except TimeoutException:
                logger.debug("VERIFY_SOLVED: Could not find success indicator in anchor iframe or timeout switching to it.")
                self.driver.switch_to.default_content() # Ensure we are back

            # Alternative: Check if the challenge iframe (bframe) has disappeared
            try:
                WebDriverWait(self.driver, 2).until_not(
                     EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='recaptcha'][src*='bframe']"))
                )
                logger.info("VERIFY_SOLVED: Success! Challenge (bframe) iframe has disappeared.")
                self._save_screenshot("09_captcha_verified_success_bframe_gone")
                return True
            except TimeoutException:
                 logger.debug("VERIFY_SOLVED: Challenge (bframe) iframe is still present.")


            # Add more checks if needed, e.g., looking for g-recaptcha-response textarea being populated
            # on the main page (self.driver.switch_to.default_content() first)
            logger.warning("VERIFY_SOLVED: Could not confirm reCAPTCHA solved with current checks.")
            self._save_screenshot("09_captcha_verification_uncertain")
            return False

        except Exception as e:
            logger.error(f"VERIFY_SOLVED: Error during verification: {e}", exc_info=True)
            try: self.driver.switch_to.default_content() # Ensure context reset
            except: pass
            return False

def main():
    """Демонстрация улучшенного решателя reCAPTCHA с полной API интеграцией"""

    try:
        # Attempt to import from config.py
        from config import CAP_GURU_API_KEY, LINKEN_SPHERE_CONFIG
        
        LINKEN_SPHERE_API_URL = LINKEN_SPHERE_CONFIG.get("api_url", "http://127.0.0.1:50325") # Default if not in config
        PROFILE_ID = LINKEN_SPHERE_CONFIG.get("profile_id", "default_profile") # Default if not in config
        TARGET_URL = LINKEN_SPHERE_CONFIG.get("target_recaptcha_url", "https://www.google.com/recaptcha/api2/demo")

    except ImportError:
        logger.warning("CONFIG: config.py not found or missing variables. Using fallback default values.")
        # Fallback настройки
        CAP_GURU_API_KEY = os.environ.get("CAP_GURU_API_KEY", "YOUR_CAP_GURU_API_KEY_HERE") # Prioritize env var
        LINKEN_SPHERE_API_URL = os.environ.get("LINKEN_SPHERE_API_URL", "http://127.0.0.1:50325")
        PROFILE_ID = os.environ.get("LINKEN_SPHERE_PROFILE_ID", "default_profile")
        TARGET_URL = "https://www.google.com/recaptcha/api2/demo"
        
        if CAP_GURU_API_KEY == "YOUR_CAP_GURU_API_KEY_HERE":
            logger.error("CONFIG: CRITICAL - CAP_GURU_API_KEY not set in environment or config.py. Please set it.")
            return

    logger.info("MAIN: Starting Advanced reCAPTCHA Solver v2.1")
    logger.info("=" * 70)
    logger.info("Features:")
    logger.info("  - Linken Sphere API for profile management")
    logger.info("  - cap.guru for coordinate-based reCAPTCHA solving")
    logger.info("  - Detailed logging and screenshots for debugging")
    logger.info("=" * 70)
    logger.info(f"Using CAP.GURU API Key: {CAP_GURU_API_KEY[:10]}...")
    logger.info(f"Using Linken Sphere API URL: {LINKEN_SPHERE_API_URL}")
    logger.info(f"Using Linken Sphere Profile ID: {PROFILE_ID}")
    logger.info(f"Target reCAPTCHA URL: {TARGET_URL}")
    logger.info("=" * 70)

    solver = AdvancedLinkenSphereRecaptchaSolver(
        cap_guru_api_key=CAP_GURU_API_KEY,
        linken_sphere_api_url=LINKEN_SPHERE_API_URL,
        profile_id=PROFILE_ID
    )

    try:
        success = solver.solve_recaptcha_full_cycle(TARGET_URL)

        if success:
            logger.info("MAIN: RESULT - CAPTCHA SOLVED SUCCESSFULLY!")
            print("\n" + "*" * 20)
            print("CAPTCHA SOLVED SUCCESSFULLY!")
            print("*" * 20)
            print(f"Check screenshots in ./{solver.screenshot_dir}")
            print("Detailed logs in linken_sphere_api_solver.log")
        else:
            logger.error("MAIN: RESULT - FAILED TO SOLVE CAPTCHA.")
            print("\n" + "!" * 20)
            print("FAILED TO SOLVE CAPTCHA.")
            print("!" * 20)
            print("Check logs (linken_sphere_api_solver.log) and screenshots for details.")
            print("Possible reasons:")
            print("  - Linken Sphere not running or API not accessible.")
            print("  - Incorrect Profile ID or Linken Sphere API URL.")
            print("  - Insufficient funds or issue with cap.guru API key.")
            print("  - Target website reCAPTCHA structure changed.")
            print("  - Network connectivity issues.")

        input("\nPress Enter to exit...")

    except KeyboardInterrupt:
        logger.info("MAIN: Process interrupted by user (KeyboardInterrupt).")
        print("\nProcess interrupted by user.")
    except Exception as e:
        logger.critical(f"MAIN: An unexpected critical error occurred: {e}", exc_info=True)
        print(f"\nAn unexpected critical error occurred: {e}")
    finally:
        # Cleanup is now handled within solve_recaptcha_full_cycle's finally block
        logger.info("MAIN: Application finished.")


if __name__ == "__main__":
    main()
