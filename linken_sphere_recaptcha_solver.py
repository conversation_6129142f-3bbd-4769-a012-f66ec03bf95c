#!/usr/bin/env python3
"""
Полноценный решатель reCAPTCHA v2 с интеграцией Linken Sphere API и cap.guru
Следует инструкции: https://docs.cap.guru/ru/apiclick/recap.html
"""

import time
import base64
import requests
import json
import os
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from PIL import Image
import io

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('recaptcha_solver.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class LinkenSphereRecaptchaSolver:
    """Решатель reCAPTCHA v2 с интеграцией Linken Sphere и cap.guru"""
    
    def __init__(self, cap_guru_api_key, linken_sphere_api_url, profile_id):
        """
        Инициализация решателя
        
        Args:
            cap_guru_api_key (str): API ключ для cap.guru
            linken_sphere_api_url (str): URL API Linken Sphere
            profile_id (str): ID профиля в Linken Sphere
        """
        self.cap_guru_api_key = cap_guru_api_key
        self.linken_sphere_api_url = linken_sphere_api_url
        self.profile_id = profile_id
        self.driver = None
        self.browser_port = None
        
        # Настройки cap.guru API
        self.cap_guru_base_url = "http://api.cap.guru"
        self.max_attempts = 30
        self.check_interval = 5
        
        # Создаем папку для скриншотов
        os.makedirs("screenshots", exist_ok=True)
        
        logger.info("🔧 Инициализация LinkenSphereRecaptchaSolver")
        logger.info(f"🔑 API ключ cap.guru: {cap_guru_api_key[:10]}...")
        logger.info(f"🌐 Linken Sphere API: {linken_sphere_api_url}")
        logger.info(f"👤 Profile ID: {profile_id}")
    
    def start_browser_profile(self):
        """Запуск браузера через API Linken Sphere"""
        logger.info("🚀 Запуск браузера через Linken Sphere API...")

        try:
            # Правильный эндпоинт для Linken Sphere
            start_url = f"{self.linken_sphere_api_url}/sessions/start"

            # Различные варианты параметров для разных версий API
            possible_data_formats = [
                {"profile_id": self.profile_id},
                {"profile_name": self.profile_id},
                {"name": self.profile_id},
                {"profileId": self.profile_id},
                {"session": {"profile_id": self.profile_id}},
                {"session": {"name": self.profile_id}}
            ]

            logger.debug(f"POST запрос к {start_url}")

            for i, start_data in enumerate(possible_data_formats, 1):
                logger.debug(f"Попытка {i}/{len(possible_data_formats)}: {start_data}")

                try:
                    response = requests.post(start_url, json=start_data, timeout=30)
                    logger.debug(f"Статус ответа: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        logger.debug(f"Ответ API: {result}")

                        # Проверяем различные форматы успешного ответа
                        if (result.get("status") == "success" or
                            result.get("success") == True or
                            "port" in result or
                            "ws_endpoint" in result):

                            # Получаем порт для подключения к браузеру
                            self.browser_port = (result.get("port") or
                                               result.get("debug_port") or
                                               result.get("debugger_port"))

                            if self.browser_port:
                                logger.info(f"✅ Профиль запущен, порт браузера: {self.browser_port}")

                                # Подключаемся к браузеру через Chrome DevTools Protocol
                                chrome_options = Options()
                                chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.browser_port}")
                                chrome_options.add_argument("--no-sandbox")
                                chrome_options.add_argument("--disable-dev-shm-usage")

                                self.driver = webdriver.Chrome(options=chrome_options)
                                self.driver.set_page_load_timeout(30)
                                self.driver.implicitly_wait(10)

                                logger.info("✅ Подключение к браузеру успешно")
                                return True
                            else:
                                logger.warning("⚠️ Профиль запущен, но порт не получен")
                                logger.debug(f"Полный ответ: {result}")
                                # Возможно, нужно подключиться к стандартному порту
                                return self._try_standard_ports()
                        else:
                            logger.debug(f"Неуспешный ответ: {result}")

                    elif response.status_code == 400:
                        try:
                            error_result = response.json()
                            logger.debug(f"Ошибка 400: {error_result}")
                        except:
                            logger.debug(f"Ошибка 400: {response.text}")
                        continue
                    else:
                        logger.debug(f"HTTP {response.status_code}: {response.text[:100]}")
                        continue

                except requests.exceptions.RequestException as e:
                    logger.debug(f"Ошибка запроса: {e}")
                    continue

            logger.error("❌ Не удалось запустить профиль ни одним из способов")
            return False
                
        except Exception as e:
            logger.error(f"❌ Ошибка при запуске браузера: {e}")
            return False

    def _try_standard_ports(self):
        """Попытка подключения к стандартным портам отладки"""
        logger.info("🔍 Попытка подключения к стандартным портам...")

        standard_ports = [9222, 9223, 9224, 9225, 9226]

        for port in standard_ports:
            try:
                logger.debug(f"Проверка порта {port}")

                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{port}")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")

                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.set_page_load_timeout(10)
                self.driver.implicitly_wait(5)

                # Проверяем, что подключение работает
                current_url = self.driver.current_url
                logger.info(f"✅ Подключение к порту {port} успешно")
                logger.info(f"📄 Текущая страница: {current_url}")

                self.browser_port = port
                return True

            except Exception as e:
                logger.debug(f"Порт {port} недоступен: {e}")
                if self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None
                continue

        logger.error("❌ Не удалось подключиться ни к одному стандартному порту")
        return False

    def connect_to_existing_browser(self):
        """Подключение к уже запущенному браузеру"""
        logger.info("🔗 Подключение к существующему браузеру...")

        try:
            if not self.browser_port:
                logger.error("❌ Порт браузера не установлен")
                return False

            # Подключаемся к браузеру через Chrome DevTools Protocol
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{self.browser_port}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)

            logger.info(f"✅ Подключение к браузеру на порту {self.browser_port} успешно")
            return True

        except Exception as e:
            logger.error(f"❌ Ошибка подключения к браузеру: {e}")
            return False

    def navigate_to_site(self, url):
        """Переход на сайт с капчей"""
        logger.info(f"🌐 Переход на сайт: {url}")
        
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # Сохраняем скриншот
            screenshot_path = f"screenshots/01_site_loaded_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            current_url = self.driver.current_url
            title = self.driver.title
            logger.info(f"📄 Страница загружена: {title}")
            logger.info(f"🔗 URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка загрузки страницы: {e}")
            return False
    
    def find_recaptcha_checkbox(self):
        """Поиск и клик по чекбоксу reCAPTCHA"""
        logger.info("🔍 Поиск чекбокса reCAPTCHA...")
        
        try:
            # Ищем основной iframe с reCAPTCHA
            recaptcha_iframe = WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='recaptcha']"))
            )
            
            logger.info("✅ Найден iframe reCAPTCHA")
            
            # Сохраняем скриншот
            screenshot_path = f"screenshots/02_recaptcha_iframe_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            # Переключаемся в iframe
            self.driver.switch_to.frame(recaptcha_iframe)
            
            # Ищем чекбокс
            checkbox_selectors = [
                "#recaptcha-anchor",
                ".recaptcha-checkbox",
                ".recaptcha-checkbox-border",
                "[role='checkbox']"
            ]
            
            checkbox = None
            for selector in checkbox_selectors:
                try:
                    checkbox = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"✅ Чекбокс найден по селектору: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not checkbox:
                logger.error("❌ Чекбокс не найден ни по одному селектору")
                self.driver.switch_to.default_content()
                return False
            
            # Кликаем по чекбоксу
            logger.info("🖱️ Клик по чекбоксу...")
            checkbox.click()
            time.sleep(3)
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            # Сохраняем скриншот после клика
            screenshot_path = f"screenshots/03_checkbox_clicked_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот сохранен: {screenshot_path}")
            
            logger.info("✅ Клик по чекбоксу выполнен")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка при поиске/клике чекбокса: {e}")
            self.driver.switch_to.default_content()
            return False
    
    def get_recaptcha_challenge(self):
        """Получение задания капчи (изображение и инструкция)"""
        logger.info("📋 Получение задания reCAPTCHA...")
        
        try:
            # Ждем появления iframe с заданием
            time.sleep(3)
            
            challenge_iframe = WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='bframe']"))
            )
            
            logger.info("✅ Найден iframe с заданием")
            
            # Переключаемся в iframe с заданием
            self.driver.switch_to.frame(challenge_iframe)
            
            # Получаем инструкцию
            instruction_selectors = [
                ".rc-imageselect-desc-no-canonical",
                ".rc-imageselect-desc",
                ".rc-imageselect-instructions",
                "[class*='instruction']"
            ]
            
            instruction_text = None
            for selector in instruction_selectors:
                try:
                    instruction_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    instruction_text = instruction_element.text.strip()
                    if instruction_text:
                        logger.info(f"✅ Инструкция найдена: {instruction_text}")
                        break
                except NoSuchElementException:
                    continue
            
            if not instruction_text:
                logger.error("❌ Не удалось найти инструкцию")
                self.driver.switch_to.default_content()
                return None, None
            
            # Получаем изображение капчи
            image_selectors = [
                ".rc-image-tile-wrapper img",
                ".rc-imageselect-payload img",
                "img[src*='payload']",
                ".rc-imageselect-target img"
            ]
            
            image_element = None
            for selector in image_selectors:
                try:
                    image_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if image_element:
                        logger.info(f"✅ Изображение найдено по селектору: {selector}")
                        break
                except NoSuchElementException:
                    continue
            
            if not image_element:
                logger.error("❌ Не удалось найти изображение капчи")
                self.driver.switch_to.default_content()
                return None, None
            
            # Получаем изображение в base64
            image_base64 = self.driver.execute_script("""
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');
                var img = arguments[0];
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                return canvas.toDataURL('image/png').split(',')[1];
            """, image_element)
            
            # Сохраняем скриншот задания
            screenshot_path = f"screenshots/04_challenge_iframe_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Скриншот задания сохранен: {screenshot_path}")
            
            # Сохраняем изображение капчи отдельно
            captcha_image_path = f"screenshots/05_captcha_image_{int(time.time())}.png"
            with open(captcha_image_path, 'wb') as f:
                f.write(base64.b64decode(image_base64))
            logger.info(f"🖼️ Изображение капчи сохранено: {captcha_image_path}")
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            logger.info(f"✅ Задание получено: '{instruction_text}'")
            logger.info(f"📏 Размер изображения: {len(image_base64)} символов")
            
            return instruction_text, image_base64
            
        except Exception as e:
            logger.error(f"❌ Ошибка получения задания: {e}")
            self.driver.switch_to.default_content()
            return None, None

    def solve_captcha_with_cap_guru(self, instruction_text, image_base64):
        """
        Отправка капчи на решение в cap.guru
        Следует инструкции: https://docs.cap.guru/ru/apiclick/recap.html
        """
        logger.info("🤖 Отправка капчи на решение в cap.guru...")

        try:
            # Подготавливаем данные для отправки
            post_data = {
                'key': self.cap_guru_api_key,
                'method': 'base64',
                'textinstructions': instruction_text,
                'body': image_base64
            }

            logger.debug(f"Отправка POST запроса к {self.cap_guru_base_url}/in.php")
            logger.debug(f"Инструкция: {instruction_text}")
            logger.debug(f"Размер изображения: {len(image_base64)} символов")

            # Отправляем POST запрос
            response = requests.post(
                f"{self.cap_guru_base_url}/in.php",
                data=post_data,
                timeout=30
            )

            if response.status_code != 200:
                logger.error(f"❌ HTTP ошибка: {response.status_code}")
                return None

            result = response.text.strip()
            logger.debug(f"Ответ сервера: {result}")

            if result.startswith("OK|"):
                task_id = result.split("|")[1]
                logger.info(f"✅ Задание создано с ID: {task_id}")
                return task_id
            else:
                logger.error(f"❌ Ошибка создания задания: {result}")
                return None

        except Exception as e:
            logger.error(f"❌ Ошибка отправки капчи: {e}")
            return None

    def get_captcha_result(self, task_id):
        """Получение результата решения капчи"""
        logger.info(f"⏳ Ожидание результата для задания {task_id}...")

        for attempt in range(self.max_attempts):
            try:
                time.sleep(self.check_interval)

                # Отправляем GET запрос для получения результата
                params = {
                    'key': self.cap_guru_api_key,
                    'action': 'get',
                    'id': task_id
                }

                response = requests.get(
                    f"{self.cap_guru_base_url}/res.php",
                    params=params,
                    timeout=30
                )

                if response.status_code != 200:
                    logger.warning(f"⚠️ HTTP ошибка при проверке: {response.status_code}")
                    continue

                result = response.text.strip()
                logger.debug(f"Попытка {attempt + 1}: {result}")

                if result == "CAPCHA_NOT_READY":
                    logger.info(f"⏳ Капча еще не готова, попытка {attempt + 1}/{self.max_attempts}")
                    continue
                elif result.startswith("OK|coordinate:"):
                    # Парсим координаты
                    coordinates_str = result.split("coordinate:")[1]
                    coordinates = []

                    # Разбираем координаты вида "x=44,y=32"
                    for coord_pair in coordinates_str.split(";"):
                        if "x=" in coord_pair and "y=" in coord_pair:
                            x = int(coord_pair.split("x=")[1].split(",")[0])
                            y = int(coord_pair.split("y=")[1])
                            coordinates.append((x, y))

                    logger.info(f"✅ Получены координаты: {coordinates}")
                    return coordinates
                else:
                    logger.error(f"❌ Ошибка решения: {result}")
                    return None

            except Exception as e:
                logger.warning(f"⚠️ Ошибка при проверке результата: {e}")
                continue

        logger.error("❌ Превышено время ожидания результата")
        return None

    def click_coordinates(self, coordinates):
        """Клик по полученным координатам"""
        logger.info(f"🖱️ Клик по координатам: {coordinates}")

        try:
            # Находим iframe с заданием
            challenge_iframe = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='bframe']"))
            )

            # Переключаемся в iframe
            self.driver.switch_to.frame(challenge_iframe)

            # Находим область с изображением
            image_area = self.driver.find_element(By.CSS_SELECTOR, ".rc-imageselect-payload, .rc-image-tile-wrapper")

            # Кликаем по каждой координате
            actions = ActionChains(self.driver)

            for i, (x, y) in enumerate(coordinates, 1):
                logger.info(f"🎯 Клик {i}/{len(coordinates)} по координатам ({x}, {y})")

                # Выполняем клик относительно элемента изображения
                actions.move_to_element_with_offset(image_area, x, y).click().perform()
                time.sleep(1)

                # Сохраняем скриншот после каждого клика
                screenshot_path = f"screenshots/06_click_{i}_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                logger.info(f"📸 Скриншот клика сохранен: {screenshot_path}")

            # Ищем и кликаем кнопку "Verify" или "Подтвердить"
            verify_selectors = [
                "#recaptcha-verify-button",
                ".rc-button-default",
                "[id*='verify']",
                "button[type='submit']"
            ]

            verify_button = None
            for selector in verify_selectors:
                try:
                    verify_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if verify_button.is_enabled():
                        break
                except NoSuchElementException:
                    continue

            if verify_button:
                logger.info("✅ Кнопка подтверждения найдена, кликаем...")
                verify_button.click()
                time.sleep(3)

                # Сохраняем скриншот после подтверждения
                screenshot_path = f"screenshots/07_verify_clicked_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                logger.info(f"📸 Скриншот подтверждения сохранен: {screenshot_path}")
            else:
                logger.warning("⚠️ Кнопка подтверждения не найдена")

            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()

            # Финальный скриншот
            screenshot_path = f"screenshots/08_final_result_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Финальный скриншот сохранен: {screenshot_path}")

            logger.info("✅ Клики по координатам выполнены")
            return True

        except Exception as e:
            logger.error(f"❌ Ошибка при кликах по координатам: {e}")
            self.driver.switch_to.default_content()
            return False

    def solve_recaptcha(self, target_url):
        """
        Полное решение reCAPTCHA v2 на указанном сайте

        Args:
            target_url (str): URL сайта с капчей

        Returns:
            bool: True если капча решена успешно
        """
        logger.info("🎯 НАЧАЛО РЕШЕНИЯ reCAPTCHA v2")
        logger.info("=" * 60)

        try:
            # Шаг 1: Запуск браузера через Linken Sphere
            if not self.start_browser_profile():
                logger.error("❌ Не удалось запустить браузер")
                return False

            # Шаг 2: Переход на сайт
            if not self.navigate_to_site(target_url):
                logger.error("❌ Не удалось перейти на сайт")
                return False

            # Шаг 3: Поиск и клик по чекбоксу
            if not self.find_recaptcha_checkbox():
                logger.error("❌ Не удалось найти/кликнуть чекбокс")
                return False

            # Ждем появления задания
            time.sleep(5)

            # Шаг 4: Получение задания капчи
            instruction_text, image_base64 = self.get_recaptcha_challenge()
            if not instruction_text or not image_base64:
                logger.error("❌ Не удалось получить задание капчи")
                return False

            # Шаг 5: Отправка на решение в cap.guru
            task_id = self.solve_captcha_with_cap_guru(instruction_text, image_base64)
            if not task_id:
                logger.error("❌ Не удалось отправить капчу на решение")
                return False

            # Шаг 6: Получение результата
            coordinates = self.get_captcha_result(task_id)
            if not coordinates:
                logger.error("❌ Не удалось получить координаты")
                return False

            # Шаг 7: Клик по координатам
            if not self.click_coordinates(coordinates):
                logger.error("❌ Не удалось кликнуть по координатам")
                return False

            logger.info("🎉 reCAPTCHA УСПЕШНО РЕШЕНА!")
            logger.info("=" * 60)
            return True

        except Exception as e:
            logger.error(f"💥 Критическая ошибка при решении reCAPTCHA: {e}")
            return False

        finally:
            # Не закрываем браузер автоматически для проверки результата
            pass

    def cleanup(self):
        """Очистка ресурсов"""
        logger.info("🧹 Очистка ресурсов...")

        try:
            if self.driver:
                self.driver.quit()
                logger.info("✅ WebDriver закрыт")

            # Останавливаем профиль в Linken Sphere
            if self.browser_port:
                stop_url = f"{self.linken_sphere_api_url}/profile/stop"
                stop_data = {"profile_id": self.profile_id}

                response = requests.post(stop_url, json=stop_data, timeout=10)
                if response.status_code == 200:
                    logger.info("✅ Профиль Linken Sphere остановлен")
                else:
                    logger.warning("⚠️ Не удалось остановить профиль")

        except Exception as e:
            logger.error(f"❌ Ошибка при очистке ресурсов: {e}")


def main():
    """Пример использования решателя"""

    # Используем существующую конфигурацию из config.py
    try:
        from config import CAP_GURU_API_KEY, LINKEN_SPHERE_CONFIG, CAP_GURU_CONFIG

        LINKEN_SPHERE_API_URL = LINKEN_SPHERE_CONFIG["api_url"]
        PROFILE_ID = LINKEN_SPHERE_CONFIG["profile_id"]
        TARGET_URL = "https://www.google.com/recaptcha/api2/demo"  # Сайт с капчей

    except ImportError:
        # Fallback настройки
        CAP_GURU_API_KEY = "47ff86fb043f86d6c563dca6ffbc2328"
        LINKEN_SPHERE_API_URL = "http://localhost:50325"
        PROFILE_ID = "Desktop preset 1"
        TARGET_URL = "https://www.google.com/recaptcha/api2/demo"

    print("🚀 ЗАПУСК РЕШАТЕЛЯ reCAPTCHA v2")
    print("=" * 60)
    print(f"🔑 API ключ: {CAP_GURU_API_KEY[:10]}...")
    print(f"🌐 Linken Sphere: {LINKEN_SPHERE_API_URL}")
    print(f"👤 Профиль: {PROFILE_ID}")
    print(f"🎯 Целевой сайт: {TARGET_URL}")
    print("=" * 60)

    # Создаем экземпляр решателя
    solver = LinkenSphereRecaptchaSolver(
        cap_guru_api_key=CAP_GURU_API_KEY,
        linken_sphere_api_url=LINKEN_SPHERE_API_URL,
        profile_id=PROFILE_ID
    )

    try:
        # Решаем капчу
        success = solver.solve_recaptcha(TARGET_URL)

        if success:
            print("\n🎉 КАПЧА УСПЕШНО РЕШЕНА!")
            print("📸 Проверьте скриншоты в папке screenshots/")
            print("📋 Детальные логи в файле recaptcha_solver.log")
        else:
            print("\n❌ НЕ УДАЛОСЬ РЕШИТЬ КАПЧУ")
            print("📋 Проверьте логи для диагностики")

        # Ждем ввод пользователя перед закрытием
        input("\nНажмите Enter для завершения...")

    except KeyboardInterrupt:
        print("\n⏹️ Процесс прерван пользователем")
    except Exception as e:
        print(f"\n💥 Критическая ошибка: {e}")
    finally:
        solver.cleanup()


if __name__ == "__main__":
    main()
