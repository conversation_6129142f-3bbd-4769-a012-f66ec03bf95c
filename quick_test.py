#!/usr/bin/env python3
"""
Быстрый тест подключения к браузеру с подробным логированием
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException

# Настройка подробного логирования
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug.log')
    ]
)
logger = logging.getLogger(__name__)

def test_browser_connection():
    """Тест подключения к браузеру с таймаутами"""
    
    logger.info("🔧 Начало теста подключения к браузеру")
    
    # Пробуем подключиться к отладочным портам
    debug_ports = [9222, 9223, 9224, 9225]
    
    for port in debug_ports:
        driver = None
        try:
            logger.info(f"📡 Попытка подключения к порту {port}")
            
            # Настройки Chrome
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            
            logger.debug(f"Создание WebDriver для порта {port}")
            
            # Устанавливаем короткий таймаут
            start_time = time.time()
            driver = webdriver.Chrome(options=chrome_options)
            
            logger.debug(f"WebDriver создан за {time.time() - start_time:.2f} сек")
            
            # Устанавливаем таймауты
            driver.set_page_load_timeout(5)
            driver.implicitly_wait(3)
            
            logger.debug("Получение текущего URL...")
            current_url = driver.current_url
            
            logger.info(f"✅ Успешно подключились к порту {port}")
            logger.info(f"📄 Текущая страница: {current_url}")
            
            # Получаем информацию о браузере
            try:
                title = driver.title
                logger.info(f"📝 Заголовок страницы: {title}")
            except Exception as e:
                logger.warning(f"Не удалось получить заголовок: {e}")
            
            return driver, port
            
        except WebDriverException as e:
            logger.debug(f"❌ Порт {port} недоступен: {str(e)[:100]}...")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            continue
            
        except Exception as e:
            logger.error(f"❌ Неожиданная ошибка на порту {port}: {e}")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            continue
    
    # Если не удалось подключиться, запускаем новый браузер
    logger.warning("🆕 Запуск нового браузера Chrome")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1200,800")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(10)
        
        logger.info("✅ Новый браузер запущен успешно")
        return driver, "new"
        
    except Exception as e:
        logger.error(f"❌ Ошибка запуска нового браузера: {e}")
        return None, None

def main():
    """Основная функция теста"""
    try:
        logger.info("🚀 Запуск быстрого теста")
        
        # Тест подключения
        driver, port = test_browser_connection()
        
        if driver:
            logger.info(f"🎉 Тест успешен! Подключение: {port}")
            
            # Простой тест навигации
            try:
                logger.info("🌐 Тест навигации на Google")
                driver.get("https://www.google.com")
                time.sleep(2)
                
                title = driver.title
                logger.info(f"📝 Заголовок Google: {title}")
                
            except Exception as e:
                logger.error(f"❌ Ошибка навигации: {e}")
            
            # Ждем ввод пользователя
            input("Нажмите Enter для закрытия браузера...")
            driver.quit()
            logger.info("🔚 Браузер закрыт")
            
        else:
            logger.error("❌ Не удалось подключиться к браузеру")
            
    except KeyboardInterrupt:
        logger.info("⏹️ Тест прерван пользователем")
    except Exception as e:
        logger.error(f"💥 Критическая ошибка: {e}")

if __name__ == "__main__":
    main() 