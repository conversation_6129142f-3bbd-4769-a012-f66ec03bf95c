# 🚀 Advanced Linken Sphere API reCAPTCHA Solver v2.0

## ✨ Новые возможности

Улучшенная версия решателя reCAPTCHA с **полной интеграцией Linken Sphere API**:

- 🔧 **Автоматический запуск профилей** через Linken Sphere API
- 🌐 **Получение WebSocket endpoint** из API ответа  
- 🎯 **Координатное решение reCAPTCHA** через cap.guru
- 📸 **Полное логирование** и скриншоты
- 🔄 **Автоматическое переподключение** при сбоях
- 🛡️ **Обработка всех типов ошибок**

## 🚀 Быстрый старт

### 1. Убедитесь, что Linken Sphere запущен

```bash
# Проверьте доступность API
curl http://localhost:50325/sessions/start
```

### 2. Настройте конфигурацию

Отредактируйте `config.py`:

```python
# API ключ cap.guru
CAP_GURU_API_KEY = "ваш_реальный_api_ключ"

# Настройки Linken Sphere
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:50325",  # URL API Linken Sphere
    "profile_id": "Desktop preset 1",    # Точное имя профиля
    "timeout": 30,
}
```

### 3. Запустите решатель

```python
from linken_sphere_recaptcha_solver import AdvancedLinkenSphereRecaptchaSolver

# Создание решателя
solver = AdvancedLinkenSphereRecaptchaSolver(
    cap_guru_api_key="ваш_api_ключ",
    linken_sphere_api_url="http://localhost:50325",
    profile_id="Desktop preset 1"
)

# Решение капчи
success = solver.solve_recaptcha_full_cycle("https://example.com")
```

## 🔧 Как это работает

### 1. Автообнаружение API эндпоинтов

Решатель автоматически тестирует различные эндпоинты:
- `/sessions/start`
- `/api/sessions/start` 
- `/profiles/start`
- `/browser/start`

### 2. Запуск профиля через API

```python
# Автоматически пробует разные форматы данных:
{"profile_id": "Desktop preset 1"}
{"name": "Desktop preset 1"}
{"profileId": "Desktop preset 1"}
```

### 3. Получение информации о браузере

Из ответа API извлекается:
- `port` - порт для DevTools подключения
- `session_id` - ID сессии для остановки
- `ws_endpoint` - WebSocket endpoint (если доступен)

### 4. Подключение через DevTools Protocol

```python
chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{port}")
```

## 📋 Логирование

Все действия логируются в файл `linken_sphere_api_solver.log`:

```
🚀 ИНИЦИАЛИЗАЦИЯ ADVANCED LINKEN SPHERE SOLVER
🔍 Обнаружение API эндпоинтов Linken Sphere...
✅ Найден рабочий эндпоинт: /sessions/start
📡 Запуск профиля: POST /sessions/start
✅ Профиль запущен успешно!
🔌 Порт браузера: 9222
```

## 🛠️ Диагностика проблем

### Проблема: API не отвечает

```bash
# Проверьте, запущен ли Linken Sphere
curl http://localhost:50325

# Проверьте доступные эндпоинты
python check_api_endpoints.py
```

### Проблема: Профиль не найден

1. Проверьте точное имя профиля в Linken Sphere
2. Убедитесь, что профиль не запущен вручную
3. Проверьте права доступа к API

### Проблема: Порт недоступен

```python
# Проверка доступности порта
import socket
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
result = sock.connect_ex(('localhost', 9222))
print("Порт открыт" if result == 0 else "Порт закрыт")
```

## 🎯 Примеры использования

### Базовое использование

```python
python advanced_api_example.py
```

### Интеграция в ваш код

```python
from linken_sphere_recaptcha_solver import AdvancedLinkenSphereRecaptchaSolver

def solve_captcha_on_site(site_url):
    solver = AdvancedLinkenSphereRecaptchaSolver(
        cap_guru_api_key="your_key",
        linken_sphere_api_url="http://localhost:50325", 
        profile_id="Your Profile"
    )
    
    return solver.solve_recaptcha_full_cycle(site_url)
```

## 🔄 Автоматическая очистка

Решатель автоматически:
- Закрывает WebDriver
- Останавливает профиль через API
- Очищает все ресурсы

## 📞 Поддержка

При проблемах проверьте:
1. Логи в `linken_sphere_api_solver.log`
2. Скриншоты в папке `screenshots/`
3. Доступность Linken Sphere API
4. Баланс на cap.guru

---

**Версия:** 2.0  
**Совместимость:** Linken Sphere с API поддержкой  
**Требования:** Python 3.7+, Selenium 4.15+
