#!/usr/bin/env python3
"""
Тест API Linken Sphere с правильными эндпоинтами
"""

import requests
import json
import time

def test_linken_sphere_api():
    """Тест подключения к API Linken Sphere с правильными эндпоинтами"""
    
    api_url = "http://localhost:50325"
    profile_id = "Desktop preset 1"
    
    print(f"🔧 Тестирование API: {api_url}")
    print(f"👤 Profile ID: {profile_id}")
    print("=" * 50)
    
    try:
        # Тест 1: Проверка доступности API
        print("1️⃣ Проверка доступности API...")
        response = requests.get(f"{api_url}/", timeout=5)
        print(f"   Статус: {response.status_code}")
        
        # Тест 2: Попытка получить список профилей
        print("2️⃣ Получение списка профилей...")
        profiles_url = f"{api_url}/api/v1/profiles"
        
        response = requests.get(profiles_url, timeout=10)
        print(f"   Статус: {response.status_code}")
        print(f"   Ответ: {response.text[:200]}...")
        
        # Тест 3: Попытка запуска профиля (разные варианты эндпоинтов)
        print("3️⃣ Попытка запуска профиля...")
        
        # Вариант 1: /api/v1/browser/start
        start_endpoints = [
            f"{api_url}/api/v1/browser/start",
            f"{api_url}/api/v1/profile/start", 
            f"{api_url}/browser/start",
            f"{api_url}/profile/start"
        ]
        
        start_data = {
            "profile_id": profile_id,
            "headless": False
        }
        
        for endpoint in start_endpoints:
            print(f"   Пробую эндпоинт: {endpoint}")
            try:
                response = requests.post(
                    endpoint, 
                    json=start_data, 
                    timeout=30
                )
                
                print(f"   Статус: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Успех! Результат: {result}")
                    
                    # Если получили порт - выводим его
                    if "port" in result:
                        print(f"   🚀 Порт браузера: {result['port']}")
                        return result
                    break
                else:
                    print(f"   ❌ Ошибка: {response.text[:100]}")
            except Exception as e:
                print(f"   ❌ Ошибка запроса: {e}")
                
        # Тест 4: Альтернативный способ - через WebSocket
        print("4️⃣ Проверка WebSocket API...")
        try:
            import websocket
            ws_url = f"ws://localhost:50325/"
            print(f"   WebSocket URL: {ws_url}")
            # Здесь можно добавить WebSocket тест
        except ImportError:
            print("   WebSocket библиотека не установлена")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Не удалось подключиться к API")
        print("   💡 Проверьте что Linken Sphere запущен и API включен")
    except Exception as e:
        print(f"   ❌ Ошибка: {e}")

if __name__ == "__main__":
    test_linken_sphere_api() 