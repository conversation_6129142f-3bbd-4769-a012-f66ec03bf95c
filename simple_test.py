#!/usr/bin/env python3
"""
Простой тест подключения к браузеру без эмодзи
"""

import time
import logging
import signal
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException

# Настройка логирования без эмодзи
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('simple_debug.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("Операция превысила таймаут")

def test_port_connection(port, timeout_seconds=10):
    """Тест подключения к конкретному порту с таймаутом"""
    
    logger.info(f"Тестирование порта {port} с таймаутом {timeout_seconds} сек")
    
    # Устанавливаем сигнал таймаута (только для Unix-систем)
    if hasattr(signal, 'SIGALRM'):
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)
    
    driver = None
    try:
        logger.debug(f"Создание Chrome options для порта {port}")
        
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        
        logger.debug("Создание WebDriver...")
        start_time = time.time()
        
        driver = webdriver.Chrome(options=chrome_options)
        
        creation_time = time.time() - start_time
        logger.debug(f"WebDriver создан за {creation_time:.2f} сек")
        
        # Устанавливаем короткие таймауты
        driver.set_page_load_timeout(3)
        driver.implicitly_wait(2)
        
        logger.debug("Получение current_url...")
        url_start = time.time()
        current_url = driver.current_url
        url_time = time.time() - url_start
        
        logger.info(f"УСПЕХ: Подключение к порту {port} за {url_time:.2f} сек")
        logger.info(f"URL: {current_url}")
        
        return driver, True
        
    except WebDriverException as e:
        logger.debug(f"WebDriverException на порту {port}: {str(e)[:200]}...")
        return None, False
        
    except TimeoutError:
        logger.warning(f"Таймаут при подключении к порту {port}")
        return None, False
        
    except Exception as e:
        logger.error(f"Неожиданная ошибка на порту {port}: {e}")
        return None, False
        
    finally:
        # Отключаем таймаут
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)
        
        # Закрываем драйвер если подключение не удалось
        if driver and not driver:
            try:
                driver.quit()
            except:
                pass

def test_new_browser():
    """Тест запуска нового браузера"""
    
    logger.info("Запуск нового браузера Chrome")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1200,800")
        chrome_options.add_argument("--disable-extensions")
        
        logger.debug("Создание нового WebDriver...")
        start_time = time.time()
        
        driver = webdriver.Chrome(options=chrome_options)
        
        creation_time = time.time() - start_time
        logger.debug(f"Новый WebDriver создан за {creation_time:.2f} сек")
        
        driver.set_page_load_timeout(10)
        
        logger.info("УСПЕХ: Новый браузер запущен")
        return driver, True
        
    except Exception as e:
        logger.error(f"Ошибка запуска нового браузера: {e}")
        return None, False

def main():
    """Основная функция"""
    
    logger.info("=== НАЧАЛО ДИАГНОСТИКИ ===")
    
    # Тестируем отладочные порты
    debug_ports = [9222, 9223, 9224, 9225]
    
    for port in debug_ports:
        logger.info(f"--- Тест порта {port} ---")
        
        driver, success = test_port_connection(port, timeout_seconds=15)
        
        if success and driver:
            logger.info(f"Порт {port} работает! Тестируем навигацию...")
            
            try:
                driver.get("https://www.google.com")
                title = driver.title
                logger.info(f"Навигация успешна. Заголовок: {title}")
                
                input("Нажмите Enter для закрытия...")
                driver.quit()
                return
                
            except Exception as e:
                logger.error(f"Ошибка навигации: {e}")
                driver.quit()
        
        logger.info(f"Порт {port} недоступен")
    
    # Если ни один порт не работает, пробуем новый браузер
    logger.info("--- Тест нового браузера ---")
    
    driver, success = test_new_browser()
    
    if success and driver:
        logger.info("Новый браузер работает! Тестируем навигацию...")
        
        try:
            driver.get("https://www.google.com")
            title = driver.title
            logger.info(f"Навигация успешна. Заголовок: {title}")
            
            input("Нажмите Enter для закрытия...")
            driver.quit()
            
        except Exception as e:
            logger.error(f"Ошибка навигации: {e}")
            driver.quit()
    else:
        logger.error("Не удалось запустить ни один браузер")
    
    logger.info("=== КОНЕЦ ДИАГНОСТИКИ ===")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Тест прерван пользователем")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        sys.exit(1) 