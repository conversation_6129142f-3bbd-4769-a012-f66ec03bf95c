# 🤖 Решатель reCAPTCHA v2 для Linken Sphere + cap.guru

Полноценный решатель reCAPTCHA v2 с интеграцией Linken Sphere API и сервиса cap.guru согласно [официальной инструкции](https://docs.cap.guru/ru/apiclick/recap.html).

## 🎯 Возможности

- ✅ **Интеграция с Linken Sphere API** - запуск браузера с прокси
- ✅ **Автоматический поиск чекбокса** - множественные селекторы
- ✅ **Извлечение задания капчи** - изображение + инструкция
- ✅ **Решение через cap.guru API** - точно по документации
- ✅ **Клик по координатам** - автоматическое выполнение
- ✅ **Подробное логирование** - каждый шаг записан
- ✅ **Сохранение скриншотов** - визуальный контроль
- ✅ **Гибкая конфигурация** - легкая настройка

## 📁 Структура файлов

```
📦 Решатель reCAPTCHA
├── 🔧 linken_sphere_recaptcha_solver.py  # Основной решатель
├── ⚙️ recaptcha_config.py                # Конфигурация
├── 🔗 integration_with_your_code.py      # Пример интеграции
├── 📋 RECAPTCHA_SOLVER_README.md         # Эта инструкция
└── 📸 screenshots/                       # Папка для скриншотов
```

## 🚀 Быстрый старт

### 1. Настройка конфигурации

Отредактируйте `recaptcha_config.py`:

```python
# API ключ cap.guru
CAP_GURU_API_KEY = "ваш_api_ключ_здесь"

# Настройки Linken Sphere
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:50325",  # Ваш порт API
    "profile_id": "ваш_profile_id",       # ID профиля
}
```

### 2. Базовое использование

```python
from linken_sphere_recaptcha_solver import LinkenSphereRecaptchaSolver

# Создание решателя
solver = LinkenSphereRecaptchaSolver(
    cap_guru_api_key="ваш_api_ключ",
    linken_sphere_api_url="http://localhost:50325",
    profile_id="ваш_profile_id"
)

# Решение капчи
success = solver.solve_recaptcha("https://example.com")

if success:
    print("🎉 Капча решена!")
else:
    print("❌ Ошибка решения")

# Очистка
solver.cleanup()
```

### 3. Запуск примера

```bash
python linken_sphere_recaptcha_solver.py
```

## 🔧 Интеграция с вашим кодом

### Если у вас уже есть код для Linken Sphere:

1. **Замените класс `YourExistingBrowserManager`** в файле `integration_with_your_code.py`
2. **Добавьте ваш код** создания браузера с прокси
3. **Используйте `IntegratedCaptchaSolver`**

```python
# Ваш существующий код
browser = YourBrowserClass(profile_id, proxy)
browser.navigate_to_site(url)

# Добавляем решение капчи
captcha_solver = IntegratedCaptchaSolver(profile_id, proxy)
success = captcha_solver.solve_captcha_on_current_page()
```

## 📋 Алгоритм работы

### Согласно [cap.guru API](https://docs.cap.guru/ru/apiclick/recap.html):

1. **🔍 Поиск чекбокса** - автоматический поиск по селекторам
2. **🖱️ Клик по чекбоксу** - активация капчи
3. **📋 Получение задания** - извлечение изображения и инструкции
4. **📤 POST запрос** к `http://api.cap.guru/in.php`:
   ```python
   {
       'key': 'ваш_api_ключ',
       'method': 'base64',
       'textinstructions': 'Pick the ladybug',
       'body': 'base64_изображение'
   }
   ```
5. **⏳ Ожидание решения** - GET запросы к `http://api.cap.guru/res.php`
6. **📍 Получение координат** - формат `OK|coordinate:x=44,y=32`
7. **🎯 Клик по координатам** - автоматическое выполнение
8. **✅ Подтверждение** - клик по кнопке "Verify"

## ⚙️ Конфигурация

### Основные настройки в `recaptcha_config.py`:

```python
# API и подключения
CAP_GURU_API_KEY = "ваш_ключ"
LINKEN_SPHERE_CONFIG = {...}

# Селекторы элементов
RECAPTCHA_SELECTORS = {
    "checkbox_selectors": [
        "#recaptcha-anchor",
        ".recaptcha-checkbox",
        "[role='checkbox']"
    ],
    "instruction_selectors": [...],
    "image_selectors": [...]
}

# Таймауты
TIMEOUTS = {
    "page_load": 30,
    "element_wait": 15,
    "after_click": 3
}
```

## 📸 Скриншоты процесса

Автоматически сохраняются:
- `01_site_loaded_*.png` - страница загружена
- `02_recaptcha_iframe_*.png` - найден iframe
- `03_checkbox_clicked_*.png` - чекбокс кликнут
- `04_challenge_iframe_*.png` - задание получено
- `05_captcha_image_*.png` - изображение капчи
- `06_click_*_*.png` - клики по координатам
- `07_verify_clicked_*.png` - подтверждение
- `08_final_result_*.png` - финальный результат

## 📋 Логирование

### Уровни логов:
- **DEBUG** - детальная отладка
- **INFO** - основные события
- **WARNING** - предупреждения
- **ERROR** - ошибки

### Файлы логов:
- `recaptcha_solver.log` - основной лог
- Консольный вывод с эмодзи

## 🔍 Отладка

### Если чекбокс не найден:
1. Проверьте селекторы в `RECAPTCHA_SELECTORS`
2. Увеличьте `TIMEOUTS["element_wait"]`
3. Включите `debug_mode = True`

### Если изображение не извлекается:
1. Проверьте селекторы изображений
2. Убедитесь, что iframe загрузился
3. Проверьте скриншоты

### Если cap.guru возвращает ошибку:
1. Проверьте API ключ
2. Убедитесь в наличии средств на балансе
3. Проверьте формат данных

## 🛠️ Требования

```bash
pip install selenium requests pillow
```

- **Python 3.7+**
- **Selenium WebDriver**
- **Linken Sphere** (запущенный)
- **Аккаунт cap.guru** с балансом
- **ChromeDriver** (автоматически)

## 🎯 Примеры использования

### Простое решение:
```python
solver = LinkenSphereRecaptchaSolver(api_key, api_url, profile_id)
success = solver.solve_recaptcha("https://target-site.com")
```

### Пошаговое решение:
```python
solver.start_browser_profile()
solver.navigate_to_site(url)
solver.find_recaptcha_checkbox()
instruction, image = solver.get_recaptcha_challenge()
task_id = solver.solve_captcha_with_cap_guru(instruction, image)
coordinates = solver.get_captcha_result(task_id)
solver.click_coordinates(coordinates)
```

### С вашим кодом:
```python
# Ваш код создания браузера
browser_port = your_browser_manager.create_browser()

# Решение капчи
solver = LinkenSphereRecaptchaSolver(...)
solver.browser_port = browser_port
success = solver.solve_recaptcha_on_current_page()
```

## 📞 Поддержка

При возникновении проблем:
1. 📋 Проверьте логи в `recaptcha_solver.log`
2. 📸 Изучите скриншоты процесса
3. ⚙️ Проверьте конфигурацию
4. 🔍 Включите режим отладки

## ✅ Готовый к работе код

Решатель полностью готов к использованию и следует официальной документации cap.guru API!
