#!/usr/bin/env python3
"""
Проверка доступных эндпоинтов API Linken Sphere
"""

import requests
import json
from urllib.parse import urljoin

def check_api_endpoints():
    """Проверка различных эндпоинтов API"""
    
    base_urls = [
        "http://localhost:50325",
        "http://localhost:35000", 
        "http://localhost:3001",
        "http://localhost:8080"
    ]
    
    # Возможные пути API
    endpoints = [
        "/",
        "/api",
        "/api/",
        "/api/v1",
        "/api/v1/",
        "/api/sessions",
        "/api/sessions/list",
        "/api/profiles",
        "/api/profiles/list", 
        "/api/profiles/start",
        "/profile/start",
        "/profiles/start",
        "/session/start",
        "/sessions/start",
        "/browser/start",
        "/status",
        "/health",
        "/ping"
    ]
    
    print("🔍 ПРОВЕРКА API ЭНДПОИНТОВ LINKEN SPHERE")
    print("=" * 60)
    
    for base_url in base_urls:
        print(f"\n🌐 Проверка базового URL: {base_url}")
        print("-" * 40)
        
        # Сначала проверим доступность порта
        try:
            response = requests.get(base_url, timeout=3)
            print(f"✅ Порт доступен: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ Порт недоступен - пропускаем")
            continue
        except Exception as e:
            print(f"⚠️ Ошибка подключения: {e}")
            continue
        
        # Проверяем эндпоинты
        working_endpoints = []
        
        for endpoint in endpoints:
            try:
                url = urljoin(base_url, endpoint)
                response = requests.get(url, timeout=2)
                
                status_icon = "✅" if response.status_code == 200 else "⚠️" if response.status_code < 500 else "❌"
                
                print(f"  {status_icon} {endpoint} → {response.status_code}")
                
                if response.status_code == 200:
                    working_endpoints.append(endpoint)
                    
                    # Показываем первые 100 символов ответа
                    try:
                        content = response.text[:100]
                        if content.strip():
                            print(f"      📄 Ответ: {content}...")
                    except:
                        pass
                        
            except requests.exceptions.Timeout:
                print(f"  ⏱️ {endpoint} → таймаут")
            except requests.exceptions.ConnectionError:
                print(f"  🔌 {endpoint} → нет соединения")
            except Exception as e:
                print(f"  ❓ {endpoint} → {str(e)[:50]}")
        
        if working_endpoints:
            print(f"\n✅ Рабочие эндпоинты на {base_url}:")
            for ep in working_endpoints:
                print(f"   • {ep}")
        else:
            print(f"\n❌ Рабочих эндпоинтов не найдено на {base_url}")

def test_profile_operations():
    """Тест операций с профилями"""
    
    print("\n" + "=" * 60)
    print("🧪 ТЕСТ ОПЕРАЦИЙ С ПРОФИЛЯМИ")
    print("=" * 60)
    
    # Возможные рабочие URL
    test_urls = [
        "http://localhost:50325",
        "http://localhost:35000"
    ]
    
    profile_id = "Desktop preset 1"
    
    for base_url in test_urls:
        print(f"\n🔧 Тестирование {base_url}")
        print("-" * 30)
        
        # Возможные варианты запуска профиля
        start_endpoints = [
            ("/api/profiles/start", {"profile_id": profile_id}),
            ("/api/profiles/start", {"name": profile_id}),
            ("/api/sessions/start", {"profile_id": profile_id}),
            ("/api/sessions/start", {"profile_name": profile_id}),
            ("/profile/start", {"profile_id": profile_id}),
            ("/profiles/start", {"profile_id": profile_id}),
            ("/browser/start", {"profile": profile_id}),
        ]
        
        for endpoint, data in start_endpoints:
            try:
                url = urljoin(base_url, endpoint)
                
                # POST запрос
                response = requests.post(
                    url, 
                    json=data, 
                    timeout=5,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"  📤 POST {endpoint}")
                print(f"     📊 Статус: {response.status_code}")
                
                if response.status_code != 404:
                    try:
                        result = response.json()
                        print(f"     📄 Ответ: {json.dumps(result, indent=2)[:200]}...")
                        
                        # Если успешно - сохраняем рабочий эндпоинт
                        if response.status_code == 200 and result.get("status") == "success":
                            print(f"     🎉 РАБОЧИЙ ЭНДПОИНТ НАЙДЕН!")
                            return base_url, endpoint, data
                            
                    except json.JSONDecodeError:
                        print(f"     📄 Ответ (текст): {response.text[:100]}...")
                        
            except requests.exceptions.ConnectionError:
                print(f"  🔌 {endpoint} → нет соединения")
                break
            except Exception as e:
                print(f"  ❓ {endpoint} → {str(e)[:50]}")
    
    return None, None, None

def main():
    """Основная функция"""
    
    # Проверяем доступные эндпоинты
    check_api_endpoints()
    
    # Тестируем операции с профилями
    working_url, working_endpoint, working_data = test_profile_operations()
    
    if working_url:
        print("\n" + "🎉" * 20)
        print("НАЙДЕН РАБОЧИЙ API!")
        print("🎉" * 20)
        print(f"URL: {working_url}")
        print(f"Эндпоинт: {working_endpoint}")
        print(f"Данные: {working_data}")
        print("\nОбновите config.py:")
        print(f'LINKEN_SPHERE_CONFIG = {{')
        print(f'    "api_url": "{working_url}",')
        print(f'    "start_endpoint": "{working_endpoint}",')
        print(f'    "profile_id": "{working_data.get("profile_id") or working_data.get("name")}",')
        print(f'}}')
    else:
        print("\n❌ РАБОЧИЙ API НЕ НАЙДЕН")
        print("Возможные причины:")
        print("1. Linken Sphere не запущен")
        print("2. API отключен в настройках")
        print("3. Используется другой порт")
        print("4. Версия не поддерживает API")

if __name__ == "__main__":
    main()
