# Быстрый старт - Решатель reCAPTCHA v2

## 🚀 Быстрая настройка (5 минут)

### 1. Установка зависимостей
```bash
pip install -r requirements.txt
```

### 2. Настройка конфигурации
Отредактируйте файл `config.py`:

```python
# API ключ для сервиса cap.guru
CAP_GURU_API_KEY = "ваш_реальный_api_ключ"

# Настройки Linken Sphere
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:35000",  # Ваш URL API
    "profile_id": "ваш_реальный_profile_id",  # Ваш ID профиля
    "timeout": 30,
}
```

### 3. Быстрый тест
```python
from improved_anticaptcha_solver import ImprovedLinkenSphereRecaptchaSolver

# Создание решателя
solver = ImprovedLinkenSphereRecaptchaSolver()

# Решение капчи
success = solver.solve_recaptcha("https://www.google.com/recaptcha/api2/demo")

if success:
    print("✅ Капча решена!")
else:
    print("❌ Ошибка")
```

## 🔧 Интеграция с вашим кодом

### Если у вас уже есть код для Linken Sphere:

1. **Замените класс `YourExistingBrowserManager`** в файле `integration_example.py` на ваш реальный код
2. **Используйте `IntegratedCaptchaSolver`** вместо создания нового браузера

```python
# Ваш существующий код
browser = YourBrowserClass(profile_id, proxy)
browser.navigate_to_site(url)

# Добавьте решение капчи
if browser.detect_captcha():
    solver = ImprovedLinkenSphereRecaptchaSolver()
    solver.driver = browser.get_driver()  # Используйте существующий драйвер
    
    # Решите только капчу
    instruction, image = solver.get_recaptcha_challenge()
    task_id = solver.solve_captcha_with_cap_guru(instruction, image)
    coordinates = solver.get_captcha_result(task_id)
    solver.click_coordinates(coordinates)
```

## 📋 Чек-лист перед запуском

- [ ] ✅ Установлены зависимости (`pip install -r requirements.txt`)
- [ ] 🔑 Указан реальный API ключ cap.guru в `config.py`
- [ ] 🌐 Указан правильный URL API Linken Sphere
- [ ] 👤 Указан правильный profile_id
- [ ] 🚀 Linken Sphere запущен и доступен
- [ ] 💰 На балансе cap.guru есть средства
- [ ] 🌍 ChromeDriver установлен или используется webdriver-manager

## 🐛 Быстрое решение проблем

### Чекбокс не найден
```python
# В config.py добавьте альтернативные селекторы
RECAPTCHA_SELECTORS = {
    "main_iframe": "iframe[src*='recaptcha'], iframe[title*='reCAPTCHA']",
    "checkbox": "#recaptcha-anchor, .recaptcha-checkbox-border",
    # ...
}
```

### Включить отладку
```python
# В config.py
ADDITIONAL_CONFIG = {
    "save_screenshots": True,  # Сохранять скриншоты
    "log_level": "DEBUG",      # Подробные логи
}
```

### Проверить логи
```bash
tail -f anticaptcha.log
```

## 📞 Поддержка

1. Проверьте файл `anticaptcha.log`
2. Включите сохранение скриншотов
3. Убедитесь в правильности конфигурации
4. Проверьте баланс cap.guru

## 🎯 Готовые примеры

- `example_usage.py` - Базовое использование
- `integration_example.py` - Интеграция с существующим кодом
- `anticaptcha_solver.py` - Простая версия
- `improved_anticaptcha_solver.py` - Полная версия с логированием 