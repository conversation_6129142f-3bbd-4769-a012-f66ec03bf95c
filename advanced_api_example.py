#!/usr/bin/env python3
"""
🚀 ПРИМЕР ИСПОЛЬЗОВАНИЯ УЛУЧШЕННОГО API РЕШАТЕЛЯ reCAPTCHA v2.0

Этот пример показывает, как использовать новый AdvancedLinkenSphereRecaptchaSolver
с полной интеграцией Linken Sphere API + cap.guru

Особенности:
- 🔧 Автоматический запуск профилей через API
- 🌐 Получение WebSocket endpoint из ответа
- 🎯 Координатное решение reCAPTCHA
- 📸 Полное логирование и скриншоты
- 🔄 Автоматическое переподключение при сбоях
"""

import sys
import os

# Добавляем текущую директорию в путь для импорта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from linken_sphere_recaptcha_solver import AdvancedLinkenSphereRecaptchaSolver

def main():
    """Основная функция демонстрации"""
    
    print("🚀 ДЕМОНСТРАЦИЯ ADVANCED LINKEN SPHERE API SOLVER")
    print("=" * 60)
    
    # Конфигурация (замените на ваши реальные данные)
    config = {
        "cap_guru_api_key": "47ff86fb043f86d6c563dca6ffbc2328",  # Ваш API ключ cap.guru
        "linken_sphere_api_url": "http://localhost:50325",        # URL API Linken Sphere
        "profile_id": "Desktop preset 1",                        # ID профиля
        "target_url": "https://www.google.com/recaptcha/api2/demo"  # Сайт с капчей
    }
    
    print(f"🔑 API ключ: {config['cap_guru_api_key'][:10]}...")
    print(f"🌐 Linken Sphere: {config['linken_sphere_api_url']}")
    print(f"👤 Профиль: {config['profile_id']}")
    print(f"🎯 Целевой сайт: {config['target_url']}")
    print("=" * 60)
    
    # Создаем экземпляр решателя
    solver = AdvancedLinkenSphereRecaptchaSolver(
        cap_guru_api_key=config["cap_guru_api_key"],
        linken_sphere_api_url=config["linken_sphere_api_url"],
        profile_id=config["profile_id"]
    )
    
    try:
        print("\n🎬 НАЧАЛО ДЕМОНСТРАЦИИ")
        print("-" * 40)
        
        # Запускаем полный цикл решения
        success = solver.solve_recaptcha_full_cycle(config["target_url"])
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
            print("✅ reCAPTCHA была решена")
            print("📸 Проверьте скриншоты в папке screenshots/")
            print("📋 Логи сохранены в linken_sphere_api_solver.log")
        else:
            print("❌ ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА С ОШИБКОЙ")
            print("📋 Проверьте логи для диагностики")
            print("\n🔧 Возможные причины:")
            print("  • Linken Sphere не запущен")
            print("  • Неправильный profile_id")
            print("  • Недостаточно средств на cap.guru")
            print("  • Проблемы с сетью")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ Демонстрация прервана пользователем")
    except Exception as e:
        print(f"\n💥 Критическая ошибка: {e}")
    
    print("\nДемонстрация завершена.")

if __name__ == "__main__":
    main()
