# Решатель reCAPTCHA v2 для Linken Sphere

Автоматическое решение reCAPTCHA v2 с использованием API браузера Linken Sphere и сервиса cap.guru.

## Возможности

- ✅ Автоматический поиск и клик по чекбоксу reCAPTCHA
- ✅ Извлечение изображения и инструкций капчи
- ✅ Отправка капчи на решение в сервис cap.guru
- ✅ Автоматический клик по полученным координатам
- ✅ Подробное логирование всех операций
- ✅ Сохранение скриншотов для отладки
- ✅ Гибкая конфигурация через config.py
- ✅ Обработка различных типов селекторов reCAPTCHA

## Установка

1. Клонируйте репозиторий:
```bash
git clone <repository_url>
cd anticaptcha-solver
```

2. Установите зависимости:
```bash
pip install -r requirements.txt
```

3. Убедитесь, что у вас установлен ChromeDriver или используйте webdriver-manager для автоматической установки.

## Настройка

### 1. Конфигурация cap.guru

Отредактируйте файл `config.py`:

```python
# API ключ для сервиса cap.guru
CAP_GURU_API_KEY = "ваш_api_ключ_здесь"
```

### 2. Настройка Linken Sphere

```python
LINKEN_SPHERE_CONFIG = {
    "api_url": "http://localhost:35000",  # URL API Linken Sphere
    "profile_id": "ваш_profile_id",       # ID профиля
    "timeout": 30,
}
```

### 3. Дополнительные настройки

```python
ADDITIONAL_CONFIG = {
    "debug_mode": True,           # Режим отладки
    "save_screenshots": True,     # Сохранять скриншоты
    "screenshot_dir": "./screenshots",  # Папка для скриншотов
    "log_level": "INFO",         # Уровень логирования
}
```

## Использование

### Базовое использование

```python
from improved_anticaptcha_solver import ImprovedLinkenSphereRecaptchaSolver

# Создание экземпляра решателя
solver = ImprovedLinkenSphereRecaptchaSolver()

# Решение капчи на конкретном сайте
success = solver.solve_recaptcha("https://example.com")

if success:
    print("Капча успешно решена!")
else:
    print("Не удалось решить капчу")
```

### Использование с кастомными параметрами

```python
solver = ImprovedLinkenSphereRecaptchaSolver(
    api_key="ваш_api_ключ",
    linken_sphere_api_url="http://localhost:35000",
    profile_id="ваш_profile_id"
)

success = solver.solve_recaptcha("https://target-site.com")
```

### Пошаговое использование

```python
solver = ImprovedLinkenSphereRecaptchaSolver()

# 1. Запуск браузера
if solver.start_browser_profile():
    # 2. Переход на сайт
    if solver.navigate_to_site("https://example.com"):
        # 3. Поиск и клик по чекбоксу
        if solver.find_recaptcha_checkbox():
            # 4. Получение задания капчи
            instruction, image = solver.get_recaptcha_challenge()
            if instruction and image:
                # 5. Отправка на решение
                task_id = solver.solve_captcha_with_cap_guru(instruction, image)
                if task_id:
                    # 6. Получение результата
                    coordinates = solver.get_captcha_result(task_id)
                    if coordinates:
                        # 7. Клик по координатам
                        solver.click_coordinates(coordinates)

# Очистка ресурсов
solver.cleanup()
```

## API сервиса cap.guru

Решатель использует следующие эндпоинты cap.guru:

### Отправка задания
```
POST http://api.cap.guru/in.php
```

Параметры:
- `key` - API ключ
- `method` - "base64"
- `textinstructions` - текст инструкции (например: "Pick the ladybug")
- `body` - изображение в формате base64

### Получение результата
```
GET http://api.cap.guru/res.php
```

Параметры:
- `key` - API ключ
- `action` - "get"
- `id` - ID задания

## Структура проекта

```
anticaptcha-solver/
├── anticaptcha_solver.py          # Базовая версия решателя
├── improved_anticaptcha_solver.py # Улучшенная версия с логированием
├── config.py                      # Конфигурация
├── requirements.txt               # Зависимости
├── README.md                      # Документация
├── anticaptcha.log               # Лог файл (создается автоматически)
└── screenshots/                   # Папка для скриншотов (создается автоматически)
    ├── 01_site_loaded.png
    ├── 02_recaptcha_iframe.png
    ├── 03_checkbox_clicked.png
    ├── 04_challenge_iframe.png
    ├── 05_captcha_image.png
    ├── 06_click_1.png
    ├── 07_verify_clicked.png
    └── 08_final_result.png
```

## Логирование

Решатель создает подробные логи в файле `anticaptcha.log` и выводит информацию в консоль. Уровни логирования:

- `DEBUG` - Детальная информация для отладки
- `INFO` - Общая информация о процессе
- `WARNING` - Предупреждения
- `ERROR` - Ошибки

## Отладка

### Включение сохранения скриншотов

В `config.py` установите:
```python
ADDITIONAL_CONFIG = {
    "save_screenshots": True,
    "screenshot_dir": "./screenshots",
}
```

### Анализ логов

Проверьте файл `anticaptcha.log` для детального анализа процесса решения капчи.

### Проверка селекторов

Если капча не обнаруживается, проверьте и обновите селекторы в `config.py`:

```python
RECAPTCHA_SELECTORS = {
    "main_iframe": "iframe[src*='recaptcha']",
    "challenge_iframe": "iframe[src*='bframe']",
    "checkbox": "#recaptcha-anchor",
    # ... другие селекторы
}
```

## Возможные проблемы и решения

### 1. Чекбокс не найден
- Проверьте, что reCAPTCHA загрузилась на странице
- Обновите селекторы в конфигурации
- Увеличьте время ожидания в настройках

### 2. Изображение капчи не извлекается
- Убедитесь, что iframe с заданием появился
- Проверьте селекторы для изображения
- Включите сохранение скриншотов для анализа

### 3. Ошибки API cap.guru
- Проверьте правильность API ключа
- Убедитесь, что у вас достаточно средств на балансе
- Проверьте формат отправляемых данных

### 4. Браузер не запускается
- Убедитесь, что Linken Sphere запущен
- Проверьте правильность URL API и profile_id
- Проверьте доступность порта для подключения

## Требования

- Python 3.7+
- Selenium WebDriver
- Linken Sphere (запущенный)
- Аккаунт в сервисе cap.guru с положительным балансом
- ChromeDriver (или автоматическая установка через webdriver-manager)

## Лицензия

MIT License

## Поддержка

При возникновении проблем:
1. Проверьте логи в файле `anticaptcha.log`
2. Убедитесь, что все зависимости установлены
3. Проверьте конфигурацию в `config.py`
4. Включите режим отладки и сохранение скриншотов 