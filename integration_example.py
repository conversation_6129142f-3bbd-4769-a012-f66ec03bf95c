#!/usr/bin/env python3
"""
Пример интеграции решателя reCAPTCHA с вашим существующим кодом
"""

from improved_anticaptcha_solver import ImprovedLinkenSphereRecaptchaSolver
import time

class YourExistingBrowserManager:
    """
    Заглушка для вашего существующего кода управления браузером
    Замените этот класс на ваш реальный код
    """
    
    def __init__(self, profile_id, proxy_config=None):
        self.profile_id = profile_id
        self.proxy_config = proxy_config
        self.browser_port = None
        
    def create_browser_with_proxy(self):
        """Ваш метод создания браузера с прокси"""
        # Здесь должен быть ваш код для создания браузера
        print(f"Создание браузера для профиля {self.profile_id}")
        print(f"Настройка прокси: {self.proxy_config}")
        
        # Возвращаем порт браузера (пример)
        self.browser_port = 9222  # Замените на реальный порт
        return self.browser_port
        
    def navigate_to_website(self, url):
        """Ваш метод перехода на сайт"""
        print(f"Переход на сайт: {url}")
        # Здесь ваш код навигации
        return True
        
    def detect_captcha(self):
        """Ваш метод обнаружения капчи"""
        print("Обнаружение капчи на странице...")
        # Здесь ваш код обнаружения
        return True  # True если капча найдена

class IntegratedCaptchaSolver:
    """
    Интегрированный решатель капчи, который использует ваш существующий код
    """
    
    def __init__(self, cap_guru_api_key, linken_sphere_config, proxy_config=None):
        self.cap_guru_api_key = cap_guru_api_key
        self.linken_sphere_config = linken_sphere_config
        self.proxy_config = proxy_config
        
        # Ваш существующий менеджер браузера
        self.browser_manager = YourExistingBrowserManager(
            profile_id=linken_sphere_config["profile_id"],
            proxy_config=proxy_config
        )
        
        # Решатель капчи
        self.captcha_solver = None
        
    def setup_browser(self):
        """Настройка браузера с использованием вашего кода"""
        try:
            # Используем ваш существующий код для создания браузера
            browser_port = self.browser_manager.create_browser_with_proxy()
            
            if browser_port:
                # Создаем решатель капчи с портом браузера
                self.captcha_solver = ImprovedLinkenSphereRecaptchaSolver(
                    api_key=self.cap_guru_api_key,
                    linken_sphere_api_url=self.linken_sphere_config["api_url"],
                    profile_id=self.linken_sphere_config["profile_id"]
                )
                
                # Подключаемся к уже запущенному браузеру
                # (модифицируем метод start_browser_profile для использования существующего порта)
                return True
            else:
                print("❌ Не удалось создать браузер")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка при настройке браузера: {e}")
            return False
    
    def process_website_with_captcha(self, target_url):
        """Основной метод обработки сайта с капчей"""
        try:
            # 1. Настройка браузера
            print("🔧 Настройка браузера...")
            if not self.setup_browser():
                return False
            
            # 2. Переход на сайт (ваш код)
            print("🌐 Переход на сайт...")
            if not self.browser_manager.navigate_to_website(target_url):
                print("❌ Не удалось перейти на сайт")
                return False
            
            # 3. Обнаружение капчи (ваш код)
            print("🔍 Обнаружение капчи...")
            if not self.browser_manager.detect_captcha():
                print("ℹ️ Капча не обнаружена")
                return True  # Капчи нет, продолжаем работу
            
            # 4. Решение капчи (наш код)
            print("🧩 Решение капчи...")
            return self.solve_detected_captcha()
            
        except Exception as e:
            print(f"💥 Ошибка при обработке сайта: {e}")
            return False
        finally:
            self.cleanup()
    
    def solve_detected_captcha(self):
        """Решение обнаруженной капчи"""
        try:
            if not self.captcha_solver:
                print("❌ Решатель капчи не инициализирован")
                return False
            
            # Используем только методы решения капчи, без создания нового браузера
            
            # 1. Поиск и клик по чекбоксу
            print("1️⃣ Поиск чекбокса reCAPTCHA...")
            if not self.captcha_solver.find_recaptcha_checkbox():
                print("❌ Чекбокс не найден")
                return False
            
            # 2. Получение задания капчи
            print("2️⃣ Получение задания капчи...")
            instruction_text, image_base64 = self.captcha_solver.get_recaptcha_challenge()
            if not instruction_text or not image_base64:
                print("❌ Не удалось получить задание капчи")
                return False
            
            print(f"📝 Инструкция: {instruction_text}")
            
            # 3. Отправка на решение в cap.guru
            print("3️⃣ Отправка капчи на решение...")
            task_id = self.captcha_solver.solve_captcha_with_cap_guru(instruction_text, image_base64)
            if not task_id:
                print("❌ Не удалось отправить капчу на решение")
                return False
            
            # 4. Получение результата
            print("4️⃣ Ожидание результата...")
            coordinates = self.captcha_solver.get_captcha_result(task_id)
            if not coordinates:
                print("❌ Не удалось получить результат")
                return False
            
            print(f"📍 Получены координаты: {coordinates}")
            
            # 5. Клик по координатам
            print("5️⃣ Клик по координатам...")
            if self.captcha_solver.click_coordinates(coordinates):
                print("✅ Капча успешно решена!")
                return True
            else:
                print("❌ Не удалось кликнуть по координатам")
                return False
                
        except Exception as e:
            print(f"💥 Ошибка при решении капчи: {e}")
            return False
    
    def cleanup(self):
        """Очистка ресурсов"""
        try:
            if self.captcha_solver:
                self.captcha_solver.cleanup()
        except Exception as e:
            print(f"⚠️ Ошибка при очистке: {e}")

def main():
    """Пример использования интегрированного решателя"""
    
    # Конфигурация
    config = {
        "cap_guru_api_key": "your_cap_guru_api_key",  # Замените на ваш API ключ
        "linken_sphere_config": {
            "api_url": "http://localhost:35000",
            "profile_id": "your_profile_id"
        },
        "proxy_config": {
            "host": "proxy.example.com",
            "port": 8080,
            "username": "user",
            "password": "pass"
        },
        "target_url": "https://example.com"  # Замените на целевой сайт
    }
    
    print("🚀 Запуск интегрированного решателя капчи")
    print("=" * 60)
    
    # Создание интегрированного решателя
    solver = IntegratedCaptchaSolver(
        cap_guru_api_key=config["cap_guru_api_key"],
        linken_sphere_config=config["linken_sphere_config"],
        proxy_config=config["proxy_config"]
    )
    
    # Обработка сайта с капчей
    success = solver.process_website_with_captcha(config["target_url"])
    
    if success:
        print("🎉 Сайт успешно обработан!")
    else:
        print("❌ Не удалось обработать сайт")

if __name__ == "__main__":
    main() 