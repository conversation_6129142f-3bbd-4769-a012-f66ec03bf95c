import requests
import time
import base64
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import io
from PIL import Image


class LinkenSphereRecaptchaSolver:
    def __init__(self, api_key, linken_sphere_api_url, profile_id):
        """
        Инициализация решателя капчи
        
        Args:
            api_key (str): API ключ для сервиса cap.guru
            linken_sphere_api_url (str): URL API Linken Sphere
            profile_id (str): ID профиля в Linken Sphere
        """
        self.api_key = api_key
        self.linken_sphere_api_url = linken_sphere_api_url
        self.profile_id = profile_id
        self.driver = None
        self.cap_guru_base_url = "http://api.cap.guru"
        
    def start_browser_profile(self):
        """Запуск браузера через API Linken Sphere"""
        try:
            # Запуск профиля
            start_url = f"{self.linken_sphere_api_url}/profile/start"
            start_data = {"profile_id": self.profile_id}
            
            response = requests.post(start_url, json=start_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("status") == "success":
                # Получаем порт для подключения к браузеру
                port = result.get("port")
                if port:
                    # Подключаемся к браузеру через Chrome DevTools Protocol
                    chrome_options = webdriver.ChromeOptions()
                    chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{port}")
                    
                    self.driver = webdriver.Chrome(options=chrome_options)
                    print(f"Браузер успешно запущен на порту {port}")
                    return True
                else:
                    print("Не удалось получить порт браузера")
                    return False
            else:
                print(f"Ошибка запуска профиля: {result}")
                return False
                
        except Exception as e:
            print(f"Ошибка при запуске браузера: {e}")
            return False
    
    def navigate_to_site(self, url):
        """Переход на указанный сайт"""
        try:
            self.driver.get(url)
            time.sleep(3)
            print(f"Переход на сайт: {url}")
            return True
        except Exception as e:
            print(f"Ошибка при переходе на сайт: {e}")
            return False
    
    def find_recaptcha_checkbox(self):
        """Поиск и клик по чекбоксу reCAPTCHA"""
        try:
            # Ждем появления iframe с reCAPTCHA
            wait = WebDriverWait(self.driver, 10)
            
            # Ищем iframe с reCAPTCHA
            recaptcha_iframe = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='recaptcha']"))
            )
            
            # Переключаемся на iframe
            self.driver.switch_to.frame(recaptcha_iframe)
            
            # Ищем чекбокс
            checkbox = wait.until(
                EC.element_to_be_clickable((By.ID, "recaptcha-anchor"))
            )
            
            # Кликаем по чекбоксу
            checkbox.click()
            print("Чекбокс reCAPTCHA найден и нажат")
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            # Ждем появления задания капчи
            time.sleep(3)
            return True
            
        except TimeoutException:
            print("Чекбокс reCAPTCHA не найден в течение 10 секунд")
            return False
        except Exception as e:
            print(f"Ошибка при поиске чекбокса reCAPTCHA: {e}")
            return False
    
    def get_recaptcha_challenge(self):
        """Получение изображения и инструкции капчи"""
        try:
            # Ищем iframe с заданием капчи
            challenge_iframe = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='bframe']"))
            )
            
            # Переключаемся на iframe с заданием
            self.driver.switch_to.frame(challenge_iframe)
            
            # Получаем текст инструкции
            instruction_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".rc-imageselect-desc-no-canonical, .rc-imageselect-desc"))
            )
            instruction_text = instruction_element.text
            print(f"Инструкция капчи: {instruction_text}")
            
            # Получаем изображение капчи
            image_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".rc-image-tile-wrapper img, .rc-imageselect-payload img"))
            )
            
            # Делаем скриншот изображения
            image_screenshot = image_element.screenshot_as_png
            
            # Конвертируем в base64
            image_base64 = base64.b64encode(image_screenshot).decode('utf-8')
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            return instruction_text, image_base64
            
        except Exception as e:
            print(f"Ошибка при получении задания капчи: {e}")
            return None, None
    
    def solve_captcha_with_cap_guru(self, instruction_text, image_base64):
        """Отправка капчи на решение в сервис cap.guru"""
        try:
            # Подготавливаем данные для отправки
            data = {
                'key': self.api_key,
                'method': 'base64',
                'textinstructions': instruction_text,
                'body': image_base64
            }
            
            # Отправляем POST запрос
            response = requests.post(f"{self.cap_guru_base_url}/in.php", data=data)
            response.raise_for_status()
            
            result = response.text
            print(f"Ответ сервера cap.guru: {result}")
            
            if result.startswith('OK|'):
                task_id = result.split('|')[1]
                print(f"Задание отправлено, ID: {task_id}")
                return task_id
            else:
                print(f"Ошибка при отправке задания: {result}")
                return None
                
        except Exception as e:
            print(f"Ошибка при отправке капчи в cap.guru: {e}")
            return None
    
    def get_captcha_result(self, task_id, max_attempts=30):
        """Получение результата решения капчи"""
        try:
            for attempt in range(max_attempts):
                # Ждем 5 секунд перед запросом
                time.sleep(5)
                
                # Отправляем GET запрос для получения результата
                params = {
                    'key': self.api_key,
                    'action': 'get',
                    'id': task_id
                }
                
                response = requests.get(f"{self.cap_guru_base_url}/res.php", params=params)
                response.raise_for_status()
                
                result = response.text
                print(f"Попытка {attempt + 1}: {result}")
                
                if result == 'CAPCHA_NOT_READY':
                    continue
                elif result.startswith('OK|'):
                    # Парсим координаты
                    coordinates_str = result.split('|')[1]
                    coordinates = self.parse_coordinates(coordinates_str)
                    print(f"Получены координаты: {coordinates}")
                    return coordinates
                else:
                    print(f"Ошибка при получении результата: {result}")
                    return None
            
            print("Превышено максимальное количество попыток получения результата")
            return None
            
        except Exception as e:
            print(f"Ошибка при получении результата капчи: {e}")
            return None
    
    def parse_coordinates(self, coordinates_str):
        """Парсинг координат из строки"""
        coordinates = []
        try:
            # Пример: "coordinate:x=44,y=32"
            if 'coordinate:' in coordinates_str:
                coord_parts = coordinates_str.split('coordinate:')[1]
                # Разделяем по запятым для множественных координат
                coord_pairs = coord_parts.split(',')
                
                x, y = None, None
                for part in coord_pairs:
                    if part.startswith('x='):
                        x = int(part.split('=')[1])
                    elif part.startswith('y='):
                        y = int(part.split('=')[1])
                        if x is not None:
                            coordinates.append((x, y))
                            x, y = None, None
                
                # Если остались непарные координаты
                if x is not None and y is not None:
                    coordinates.append((x, y))
            
            return coordinates
        except Exception as e:
            print(f"Ошибка при парсинге координат: {e}")
            return []
    
    def click_coordinates(self, coordinates):
        """Клик по полученным координатам"""
        try:
            # Переключаемся на iframe с заданием
            challenge_iframe = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='bframe']"))
            )
            self.driver.switch_to.frame(challenge_iframe)
            
            # Находим область с изображениями
            image_area = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".rc-imageselect-payload, .rc-image-tile-wrapper"))
            )
            
            # Кликаем по каждой координате
            actions = ActionChains(self.driver)
            for x, y in coordinates:
                print(f"Клик по координатам: x={x}, y={y}")
                actions.move_to_element_with_offset(image_area, x, y).click().perform()
                time.sleep(1)
            
            # Ищем и нажимаем кнопку "Подтвердить"
            verify_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.ID, "recaptcha-verify-button"))
            )
            verify_button.click()
            print("Нажата кнопка подтверждения")
            
            # Возвращаемся к основному контенту
            self.driver.switch_to.default_content()
            
            # Ждем завершения проверки
            time.sleep(3)
            return True
            
        except Exception as e:
            print(f"Ошибка при клике по координатам: {e}")
            return False
    
    def solve_recaptcha(self, url):
        """Основной метод для решения reCAPTCHA"""
        try:
            # Запускаем браузер
            if not self.start_browser_profile():
                return False
            
            # Переходим на сайт
            if not self.navigate_to_site(url):
                return False
            
            # Ищем и кликаем чекбокс
            if not self.find_recaptcha_checkbox():
                return False
            
            # Получаем задание капчи
            instruction_text, image_base64 = self.get_recaptcha_challenge()
            if not instruction_text or not image_base64:
                return False
            
            # Отправляем на решение
            task_id = self.solve_captcha_with_cap_guru(instruction_text, image_base64)
            if not task_id:
                return False
            
            # Получаем результат
            coordinates = self.get_captcha_result(task_id)
            if not coordinates:
                return False
            
            # Кликаем по координатам
            if not self.click_coordinates(coordinates):
                return False
            
            print("reCAPTCHA успешно решена!")
            return True
            
        except Exception as e:
            print(f"Ошибка при решении reCAPTCHA: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Очистка ресурсов"""
        try:
            if self.driver:
                self.driver.quit()
            
            # Останавливаем профиль в Linken Sphere
            stop_url = f"{self.linken_sphere_api_url}/profile/stop"
            stop_data = {"profile_id": self.profile_id}
            requests.post(stop_url, json=stop_data)
            
        except Exception as e:
            print(f"Ошибка при очистке ресурсов: {e}")


# Пример использования
if __name__ == "__main__":
    # Настройки
    CAP_GURU_API_KEY = "your_cap_guru_api_key"  # Замените на ваш API ключ
    LINKEN_SPHERE_API_URL = "http://localhost:35000"  # URL API Linken Sphere
    PROFILE_ID = "your_profile_id"  # ID профиля в Linken Sphere
    TARGET_URL = "https://example.com"  # URL сайта с капчей
    
    # Создаем экземпляр решателя
    solver = LinkenSphereRecaptchaSolver(
        api_key=CAP_GURU_API_KEY,
        linken_sphere_api_url=LINKEN_SPHERE_API_URL,
        profile_id=PROFILE_ID
    )
    
    # Решаем капчу
    success = solver.solve_recaptcha(TARGET_URL)
    
    if success:
        print("Капча успешно решена!")
    else:
        print("Не удалось решить капчу") 