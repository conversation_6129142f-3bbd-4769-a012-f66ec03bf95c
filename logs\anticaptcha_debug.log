2025-05-31 08:29:32 - __main__ - DEBUG - test_logging_system:20 - 🔍 Это DEBUG сообщение - видно только в файлах
2025-05-31 08:29:32 - improved_anticaptcha_solver - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:29:32 - improved_anticaptcha_solver - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:29:32 - improved_anticaptcha_solver - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:29:32 - improved_anticaptcha_solver - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:29:32 - improved_anticaptcha_solver - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:29:41 - improved_anticaptcha_solver - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:29:41 - improved_anticaptcha_solver - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:29:41 - improved_anticaptcha_solver - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:29:41 - improved_anticaptcha_solver - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:29:42 - improved_anticaptcha_solver - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:29:42 - improved_anticaptcha_solver - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9222
2025-05-31 08:29:42 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:29:42 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:29:46 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:29:47 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13656 using 0 to output -3
2025-05-31 08:29:48 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:58139/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:29:48 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:58139
2025-05-31 08:31:33 - __main__ - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:31:33 - __main__ - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:31:33 - __main__ - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:31:33 - __main__ - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:31:33 - __main__ - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:31:33 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9222
2025-05-31 08:31:33 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:31:33 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:31:34 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:31:34 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 12396 using 0 to output -3
2025-05-31 08:31:34 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:58355/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:31:34 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:58355
2025-05-31 08:35:48 - __main__ - DEBUG - demo_logging_features:22 - 🔍 DEBUG: Детальная отладочная информация
2025-05-31 08:35:48 - improved_anticaptcha_solver - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:35:48 - improved_anticaptcha_solver - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:35:48 - improved_anticaptcha_solver - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:35:48 - improved_anticaptcha_solver - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:35:48 - improved_anticaptcha_solver - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:35:48 - __main__ - DEBUG - demo_logging_features:50 - 🔍 Детали шага 1: операция выполнена за 0.5с
2025-05-31 08:35:49 - __main__ - DEBUG - demo_logging_features:50 - 🔍 Детали шага 2: операция выполнена за 0.5с
2025-05-31 08:35:49 - __main__ - DEBUG - demo_logging_features:50 - 🔍 Детали шага 3: операция выполнена за 0.5с
2025-05-31 08:35:50 - __main__ - DEBUG - demo_logging_features:50 - 🔍 Детали шага 4: операция выполнена за 0.5с
2025-05-31 08:35:50 - __main__ - DEBUG - demo_logging_features:50 - 🔍 Детали шага 5: операция выполнена за 0.5с
2025-05-31 08:35:50 - __main__ - DEBUG - demo_logging_features:61 - 🔍 Полная информация об ошибке:
Traceback (most recent call last):
  File "F:\home\m\Воркзилла\demo_logging.py", line 58, in demo_logging_features
    raise ValueError("Это тестовая ошибка для демонстрации логирования")
ValueError: Это тестовая ошибка для демонстрации логирования
2025-05-31 08:36:19 - __main__ - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:36:19 - __main__ - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:36:19 - __main__ - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:36:19 - __main__ - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:36:19 - __main__ - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:36:19 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9222
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:36:19 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:36:19 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 14152 using 0 to output -3
2025-05-31 08:36:20 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:58720/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:36:20 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:58720
2025-05-31 08:37:21 - urllib3.connectionpool - DEBUG - _make_request:475 - http://localhost:58720 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:37:21 - selenium.webdriver.remote.remote_connection - DEBUG - _request:330 - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9222\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:37:21 - selenium.webdriver.remote.remote_connection - DEBUG - _request:357 - Finished Request
2025-05-31 08:37:22 - __main__ - DEBUG - setup_driver:157 - ❌ Порт 9222 недоступен: Message: session not created: cannot connect to chrome at localhost:9222
from chrome not reachable
S...
2025-05-31 08:37:24 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9223
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:37:24 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:37:24 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 14292 using 0 to output -3
2025-05-31 08:37:25 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:58962/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9223', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:37:25 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:58962
2025-05-31 08:38:26 - urllib3.connectionpool - DEBUG - _make_request:475 - http://localhost:58962 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:38:26 - selenium.webdriver.remote.remote_connection - DEBUG - _request:330 - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9223\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:38:26 - selenium.webdriver.remote.remote_connection - DEBUG - _request:357 - Finished Request
2025-05-31 08:38:28 - __main__ - DEBUG - setup_driver:157 - ❌ Порт 9223 недоступен: Message: session not created: cannot connect to chrome at localhost:9223
from chrome not reachable
S...
2025-05-31 08:38:28 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9224
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:38:28 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:38:28 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13964 using 0 to output -3
2025-05-31 08:38:29 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:59277/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9224', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:38:29 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:59277
2025-05-31 08:39:30 - urllib3.connectionpool - DEBUG - _make_request:475 - http://localhost:59277 "POST /session HTTP/1.1" 500 1025
2025-05-31 08:39:30 - selenium.webdriver.remote.remote_connection - DEBUG - _request:330 - Remote response: status=500 | data={"value":{"error":"session not created","message":"session not created: cannot connect to chrome at localhost:9224\nfrom chrome not reachable","stacktrace":"\tGetHandleVerifier [0x0x7ff63fd26f65+78965]\n\tGetHandleVerifier [0x0x7ff63fd26fc0+79056]\n\t(No symbol) [0x0x7ff63fab9c0c]\n\t(No symbol) [0x0x7ff63faaaa41]\n\t(No symbol) [0x0x7ff63fafceb2]\n\t(No symbol) [0x0x7ff63faf208e]\n\t(No symbol) [0x0x7ff63fb465be]\n\t(No symbol) [0x0x7ff63fb45d50]\n\t(No symbol) [0x0x7ff63fb38443]\n\t(No symbol) [0x0x7ff63fb01311]\n\t(No symbol) [0x0x7ff63fb020a3]\n\tGetHandleVerifier [0x0x7ff63ffde26d+2926461]\n\tGetHandleVerifier [0x0x7ff63ffd8993+2903715]\n\tGetHandleVerifier [0x0x7ff63fff6aed+3026941]\n\tGetHandleVerifier [0x0x7ff63fd416fe+187406]\n\tGetHandleVerifier [0x0x7ff63fd496ef+220159]\n\tGetHandleVerifier [0x0x7ff63fd2faf4+114692]\n\tGetHandleVerifier [0x0x7ff63fd2fca9+115129]\n\tGetHandleVerifier [0x0x7ff63fd164d8+10728]\n\tBaseThreadInitThunk [0x0x7ffc058ce8d7+23]\n\tRtlUserThreadStart [0x0x7ffc074714fc+44]\n"}} | headers=HTTPHeaderDict({'Content-Length': '1025', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-05-31 08:39:30 - selenium.webdriver.remote.remote_connection - DEBUG - _request:357 - Finished Request
2025-05-31 08:39:31 - __main__ - DEBUG - setup_driver:157 - ❌ Порт 9224 недоступен: Message: session not created: cannot connect to chrome at localhost:9224
from chrome not reachable
S...
2025-05-31 08:39:31 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9225
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:39:31 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:39:31 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 13104 using 0 to output -3
2025-05-31 08:39:32 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:59578/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9225', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:39:32 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:59578
2025-05-31 08:42:09 - __main__ - DEBUG - __init__:97 - API ключ: 47ff86fb04...2328
2025-05-31 08:42:09 - __main__ - DEBUG - __init__:98 - Базовый URL: http://api.cap.guru
2025-05-31 08:42:09 - __main__ - DEBUG - __init__:99 - Таймаут: 30с
2025-05-31 08:42:09 - __main__ - DEBUG - __init__:100 - Максимум попыток: 30
2025-05-31 08:42:09 - __main__ - DEBUG - __init__:101 - Интервал проверки: 5с
2025-05-31 08:42:09 - __main__ - DEBUG - setup_driver:132 - Создание WebDriver с опциями: debuggerAddress=localhost:9222
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - get_binary:75 - Selenium Manager binary found at: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:134 - Executing process: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\selenium\webdriver\common\windows\selenium-manager.exe --browser chrome --debug --language-binding python --output json
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver not found in PATH
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chrome detected at C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: wmic datafile where name='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe' get Version /value
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: ""
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Running command: REG QUERY HKCU\Software\Google\Chrome\BLBeacon /v version
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Output: "\r\nHKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon\r\n    version    REG_SZ    137.0.7151.41\r\n"
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Detected browser: chrome 137.0.7151.41
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Required driver: chromedriver 137.0.7151.55
2025-05-31 08:42:09 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - chromedriver 137.0.7151.55 already in the cache
2025-05-31 08:42:10 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Driver path: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:42:10 - selenium.webdriver.common.selenium_manager - DEBUG - run:151 - Browser path: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-05-31 08:42:10 - selenium.webdriver.common.selenium_manager - DEBUG - driver_location:110 - Using driver at: C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe
2025-05-31 08:42:10 - selenium.webdriver.common.service - DEBUG - _start_process:219 - Started executable: `C:\Users\<USER>\.cache\selenium\chromedriver\win64\137.0.7151.55\chromedriver.exe` in a child process with pid: 6440 using 0 to output -3
2025-05-31 08:42:10 - selenium.webdriver.remote.remote_connection - DEBUG - execute:301 - POST http://localhost:60007/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'browserVersion': None, 'goog:chromeOptions': {'debuggerAddress': 'localhost:9222', 'extensions': [], 'binary': 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', 'args': ['--no-sandbox', '--disable-dev-shm-usage']}}}}
2025-05-31 08:42:10 - urllib3.connectionpool - DEBUG - _new_conn:246 - Starting new HTTP connection (1): localhost:60007
