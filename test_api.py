#!/usr/bin/env python3
"""
Тест API Linken Sphere с правильным портом
"""

import requests
import json

def test_linken_sphere_api():
    """Тест подключения к API Linken Sphere"""
    
    api_url = "http://localhost:50325"
    profile_id = "Desktop preset 1"
    
    print(f"🔧 Тестирование API: {api_url}")
    print(f"👤 Profile ID: {profile_id}")
    print("=" * 50)
    
    try:
        # Тест 1: Проверка доступности API
        print("1️⃣ Проверка доступности API...")
        response = requests.get(f"{api_url}/", timeout=5)
        print(f"   Статус: {response.status_code}")
        
        # Тест 2: Попытка запуска профиля
        print("2️⃣ Попытка запуска профиля...")
        start_url = f"{api_url}/profile/start"
        start_data = {"profile_id": profile_id}
        
        response = requests.post(
            start_url, 
            json=start_data, 
            timeout=30
        )
        
        print(f"   Статус: {response.status_code}")
        print(f"   Ответ: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Успех! Результат: {result}")
            
            # Если получили порт - выводим его
            if "port" in result:
                print(f"   🚀 Порт браузера: {result['port']}")
        else:
            print(f"   ❌ Ошибка: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Не удалось подключиться к API")
        print("   💡 Проверьте что Linken Sphere запущен и API включен")
    except Exception as e:
        print(f"   ❌ Ошибка: {e}")

if __name__ == "__main__":
    test_linken_sphere_api() 