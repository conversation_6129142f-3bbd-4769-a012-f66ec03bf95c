#!/usr/bin/env python3
"""
Улучшенный решатель reCAPTCHA v2 для Linken Sphere
Работает без API, подключается напрямую к браузеру
"""

import time
import base64
import requests
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import logging
from config import CAP_GURU_API_KEY, CAP_GURU_CONFIG, SELENIUM_CONFIG, RECAPTCHA_SELECTORS, ADDITIONAL_CONFIG

# Настройка улучшенного логирования
def setup_logging():
    """Настройка системы логирования с файлами и консолью"""

    # Создаем папку для логов
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    # Получаем корневой логгер
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # Очищаем существующие обработчики
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Формат для логов
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

    # Обработчик для консоли (только INFO и выше)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, ADDITIONAL_CONFIG.get("log_level", "INFO")))
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)

    # Обработчик для основного лог-файла (все уровни)
    main_log_file = os.path.join(log_dir, "anticaptcha_main.log")
    file_handler = logging.FileHandler(main_log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)

    # Обработчик для ошибок (только ERROR и CRITICAL)
    error_log_file = os.path.join(log_dir, "anticaptcha_errors.log")
    error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)

    # Обработчик для отладки (только DEBUG)
    debug_log_file = os.path.join(log_dir, "anticaptcha_debug.log")
    debug_handler = logging.FileHandler(debug_log_file, encoding='utf-8')
    debug_handler.setLevel(logging.DEBUG)
    debug_handler.addFilter(lambda record: record.levelno == logging.DEBUG)
    debug_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(debug_handler)

    return logging.getLogger(__name__)

# Инициализируем логирование
logger = setup_logging()
logger.info("=" * 60)
logger.info("СИСТЕМА ЛОГИРОВАНИЯ ИНИЦИАЛИЗИРОВАНА")
logger.info("=" * 60)

class ImprovedAntiCaptchaSolver:
    """Улучшенный решатель reCAPTCHA v2 с интеграцией cap.guru"""
    
    def __init__(self):
        logger.info("🔧 Инициализация ImprovedAntiCaptchaSolver")

        self.api_key = CAP_GURU_API_KEY
        self.base_url = CAP_GURU_CONFIG["base_url"]
        self.timeout = CAP_GURU_CONFIG["timeout"]
        self.max_attempts = CAP_GURU_CONFIG["max_attempts"]
        self.check_interval = CAP_GURU_CONFIG["check_interval"]
        self.driver = None

        logger.debug(f"API ключ: {self.api_key[:10]}...{self.api_key[-4:] if len(self.api_key) > 14 else 'короткий'}")
        logger.debug(f"Базовый URL: {self.base_url}")
        logger.debug(f"Таймаут: {self.timeout}с")
        logger.debug(f"Максимум попыток: {self.max_attempts}")
        logger.debug(f"Интервал проверки: {self.check_interval}с")

        # Создаем папку для скриншотов
        if ADDITIONAL_CONFIG["save_screenshots"]:
            screenshot_dir = ADDITIONAL_CONFIG["screenshot_dir"]
            os.makedirs(screenshot_dir, exist_ok=True)
            logger.info(f"📸 Папка для скриншотов: {screenshot_dir}")
        else:
            logger.info("📸 Сохранение скриншотов отключено")

        logger.info("✅ Инициализация завершена успешно")
    
    def setup_driver(self):
        """Настройка драйвера Chrome для подключения к существующему браузеру"""
        logger.info("🌐 Начало настройки WebDriver")

        try:
            # Пробуем подключиться к отладочному порту Linken Sphere
            debug_ports = [9222, 9223, 9224, 9225]  # Обычные порты для отладки
            logger.info(f"🔍 Поиск активных браузеров на портах: {debug_ports}")

            for i, port in enumerate(debug_ports, 1):
                try:
                    logger.info(f"📡 [{i}/{len(debug_ports)}] Попытка подключения к Chrome на порту {port}")

                    # Настройки Chrome для подключения к существующему браузеру
                    chrome_options = Options()
                    chrome_options.add_experimental_option("debuggerAddress", f"localhost:{port}")
                    chrome_options.add_argument("--no-sandbox")
                    chrome_options.add_argument("--disable-dev-shm-usage")

                    logger.debug(f"Создание WebDriver с опциями: debuggerAddress=localhost:{port}")

                    # Устанавливаем очень короткий таймаут для быстрой проверки
                    start_time = time.time()

                    # Устанавливаем таймаут для подключения
                    chrome_options.add_argument("--connect-timeout=3")

                    self.driver = webdriver.Chrome(options=chrome_options)
                    connection_time = time.time() - start_time

                    logger.debug(f"WebDriver создан за {connection_time:.2f}с")

                    self.driver.set_page_load_timeout(10)
                    self.driver.implicitly_wait(5)

                    # Проверяем что подключение работает
                    logger.debug("Получение текущего URL для проверки подключения...")
                    current_url = self.driver.current_url
                    window_handles = len(self.driver.window_handles)

                    logger.info(f"✅ Успешно подключились к браузеру на порту {port}")
                    logger.info(f"📄 Текущая страница: {current_url}")
                    logger.info(f"🪟 Количество вкладок: {window_handles}")
                    logger.debug(f"Время подключения: {connection_time:.2f}с")

                    return True

                except WebDriverException as e:
                    logger.debug(f"❌ Порт {port} недоступен: {str(e)[:100]}...")
                    if self.driver:
                        try:
                            self.driver.quit()
                        except:
                            pass
                        self.driver = None
                    continue
                except Exception as e:
                    logger.warning(f"⚠️ Неожиданная ошибка на порту {port}: {str(e)[:100]}...")
                    if self.driver:
                        try:
                            self.driver.quit()
                        except:
                            pass
                        self.driver = None
                    continue
            
            # Если не удалось подключиться к существующему браузеру, запускаем новый
            logger.warning("⚠️ Не удалось подключиться к существующему браузеру")
            logger.info("🆕 Запуск нового браузера Chrome...")

            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            logger.debug("Создание нового WebDriver с опциями:")
            logger.debug("  --no-sandbox")
            logger.debug("  --disable-dev-shm-usage")
            logger.debug("  --disable-gpu")
            logger.debug("  --window-size=1920,1080")

            start_time = time.time()
            self.driver = webdriver.Chrome(options=chrome_options)
            creation_time = time.time() - start_time

            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)

            logger.info(f"✅ Новый браузер Chrome запущен за {creation_time:.2f}с")
            logger.debug(f"Таймаут загрузки страницы: 30с")
            logger.debug(f"Неявное ожидание: 10с")

            return True

        except Exception as e:
            logger.error(f"💥 Критическая ошибка настройки драйвера: {e}")
            logger.debug(f"Полная ошибка: {e}", exc_info=True)
            return False
    
    def solve_recaptcha(self, site_key, page_url):
        """
        Решение reCAPTCHA v2 с помощью cap.guru
        
        Args:
            site_key (str): Ключ сайта reCAPTCHA
            page_url (str): URL страницы с капчей
            
        Returns:
            str: Токен решения или None при ошибке
        """
        try:
            # Шаг 1: Отправка задачи на решение
            task_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "NoCaptchaTaskProxyless",
                    "websiteURL": page_url,
                    "websiteKey": site_key
                }
            }
            
            logger.info("Отправка задачи на cap.guru...")
            response = requests.post(
                f"{self.base_url}/createTask",
                json=task_data,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                logger.error(f"Ошибка HTTP: {response.status_code}")
                return None
            
            result = response.json()
            if result.get("errorId") != 0:
                logger.error(f"Ошибка API: {result.get('errorDescription')}")
                return None
            
            task_id = result.get("taskId")
            logger.info(f"Задача создана с ID: {task_id}")
            
            # Шаг 2: Ожидание решения
            for attempt in range(self.max_attempts):
                time.sleep(self.check_interval)
                
                check_data = {
                    "clientKey": self.api_key,
                    "taskId": task_id
                }
                
                response = requests.post(
                    f"{self.base_url}/getTaskResult",
                    json=check_data,
                    timeout=self.timeout
                )
                
                if response.status_code != 200:
                    logger.warning(f"Ошибка проверки статуса: {response.status_code}")
                    continue
                
                result = response.json()
                
                if result.get("status") == "ready":
                    solution = result.get("solution", {}).get("gRecaptchaResponse")
                    logger.info("✅ reCAPTCHA решена успешно!")
                    return solution
                
                elif result.get("status") == "processing":
                    logger.info(f"Обработка... Попытка {attempt + 1}/{self.max_attempts}")
                    continue
                
                else:
                    logger.error(f"Ошибка решения: {result}")
                    return None
            
            logger.error("Превышено время ожидания решения")
            return None
            
        except Exception as e:
            logger.error(f"Ошибка при решении reCAPTCHA: {e}")
            return None
    
    def find_recaptcha_elements(self):
        """Поиск элементов reCAPTCHA на странице"""
        try:
            # Ищем iframe с reCAPTCHA
            recaptcha_iframe = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, RECAPTCHA_SELECTORS["main_iframe"]))
            )
            
            # Получаем site_key из iframe
            iframe_src = recaptcha_iframe.get_attribute("src")
            site_key = None
            
            if "k=" in iframe_src:
                site_key = iframe_src.split("k=")[1].split("&")[0]
            
            logger.info(f"Найден reCAPTCHA iframe, site_key: {site_key}")
            return site_key
            
        except TimeoutException:
            logger.warning("reCAPTCHA iframe не найден")
            return None
        except Exception as e:
            logger.error(f"Ошибка поиска reCAPTCHA: {e}")
            return None
    
    def inject_solution(self, solution_token):
        """Внедрение решения reCAPTCHA на страницу"""
        try:
            # Скрипт для внедрения токена
            inject_script = f"""
            // Находим все элементы textarea с именем g-recaptcha-response
            var textareas = document.querySelectorAll('textarea[name="g-recaptcha-response"]');
            
            for (var i = 0; i < textareas.length; i++) {{
                textareas[i].style.display = 'block';
                textareas[i].value = '{solution_token}';
                
                // Создаем и отправляем событие change
                var event = new Event('change', {{ bubbles: true }});
                textareas[i].dispatchEvent(event);
            }}
            
            // Вызываем callback функцию если она есть
            if (typeof window.grecaptcha !== 'undefined' && window.grecaptcha.getResponse) {{
                try {{
                    // Находим виджет reCAPTCHA
                    var widgets = document.querySelectorAll('.g-recaptcha');
                    for (var j = 0; j < widgets.length; j++) {{
                        var widgetId = widgets[j].getAttribute('data-widget-id');
                        if (widgetId) {{
                            window.grecaptcha.execute(widgetId);
                        }}
                    }}
                }} catch (e) {{
                    console.log('Ошибка выполнения grecaptcha:', e);
                }}
            }}
            
            return 'Токен внедрен успешно';
            """
            
            result = self.driver.execute_script(inject_script)
            logger.info(f"Результат внедрения: {result}")
            
            # Сохраняем скриншот после внедрения
            if ADDITIONAL_CONFIG["save_screenshots"]:
                screenshot_path = os.path.join(
                    ADDITIONAL_CONFIG["screenshot_dir"], 
                    f"after_injection_{int(time.time())}.png"
                )
                self.driver.save_screenshot(screenshot_path)
                logger.info(f"Скриншот сохранен: {screenshot_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка внедрения решения: {e}")
            return False
    
    def solve_page_recaptcha(self, url=None):
        """
        Основной метод для решения reCAPTCHA на текущей странице
        
        Args:
            url (str, optional): URL для перехода. Если None, работает с текущей страницей
            
        Returns:
            bool: True если успешно решено, False при ошибке
        """
        try:
            if not self.setup_driver():
                return False
            
            # Переходим на страницу если указан URL
            if url:
                logger.info(f"Переход на страницу: {url}")
                self.driver.get(url)
                time.sleep(3)
            
            current_url = self.driver.current_url
            logger.info(f"Текущая страница: {current_url}")
            
            # Сохраняем скриншот до решения
            if ADDITIONAL_CONFIG["save_screenshots"]:
                screenshot_path = os.path.join(
                    ADDITIONAL_CONFIG["screenshot_dir"], 
                    f"before_solving_{int(time.time())}.png"
                )
                self.driver.save_screenshot(screenshot_path)
                logger.info(f"Скриншот сохранен: {screenshot_path}")
            
            # Ищем reCAPTCHA на странице
            site_key = self.find_recaptcha_elements()
            if not site_key:
                logger.error("reCAPTCHA не найдена на странице")
                return False
            
            # Решаем reCAPTCHA
            solution_token = self.solve_recaptcha(site_key, current_url)
            if not solution_token:
                logger.error("Не удалось получить решение reCAPTCHA")
                return False
            
            # Внедряем решение
            if self.inject_solution(solution_token):
                logger.info("✅ reCAPTCHA успешно решена и внедрена!")
                return True
            else:
                logger.error("Ошибка внедрения решения")
                return False
                
        except Exception as e:
            logger.error(f"Ошибка решения reCAPTCHA: {e}")
            return False
        
        finally:
            # Не закрываем драйвер, оставляем браузер открытым
            pass
    
    def close(self):
        """Закрытие драйвера"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Драйвер закрыт")
            except:
                pass

def main():
    """Основная функция для тестирования"""
    solver = ImprovedAntiCaptchaSolver()
    
    try:
        # Пример использования - решение reCAPTCHA на тестовой странице
        test_url = "https://www.google.com/recaptcha/api2/demo"
        
        logger.info("🚀 Запуск решателя reCAPTCHA")
        logger.info(f"API ключ: {CAP_GURU_API_KEY[:10]}...")
        
        success = solver.solve_page_recaptcha(test_url)
        
        if success:
            logger.info("🎉 Тест завершен успешно!")
            input("Нажмите Enter для закрытия браузера...")
        else:
            logger.error("❌ Тест завершился с ошибкой")
            
    except KeyboardInterrupt:
        logger.info("Прервано пользователем")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
    finally:
        solver.close()

if __name__ == "__main__":
    main() 